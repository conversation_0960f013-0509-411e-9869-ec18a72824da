#!/usr/bin/env python3
"""
策略引擎测试
"""

import sys
import time
from pathlib import Path
from datetime import datetime, date
from decimal import Decimal

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from strategies.base_strategy import BaseStrategy
from strategies.examples.ma_cross_strategy import MACrossStrategy
from strategy_ai_engine.backtester import BacktestEngine
from strategy_ai_engine.live_trader import LiveTrader, StrategyManager
from core.data_types import BarData, Interval, TickData
from core.event_types import SignalEvent
from infrastructure.logger import setup_logger


class TestStrategy(BaseStrategy):
    """测试策略"""
    
    def on_init(self):
        self.subscribed_symbols = ['TEST']
        self.bar_count = 0
    
    def on_start(self):
        self.logger.info("测试策略启动")
    
    def on_stop(self):
        self.logger.info("测试策略停止")
    
    def on_tick(self, tick):
        pass
    
    def on_bar(self, bar):
        self.bar_count += 1
        if self.bar_count % 5 == 0:
            # 每5根K线发送一个买入信号
            self.buy(bar.symbol, 100)


def test_base_strategy():
    """测试策略基类"""
    print("测试策略基类...")
    
    # 创建测试策略
    strategy = TestStrategy('test_strategy', {'param1': 'value1'})
    
    # 测试初始化
    assert strategy.initialize(), "策略初始化失败"
    assert strategy.initialized, "策略初始化状态错误"
    
    # 测试启动
    assert strategy.start(), "策略启动失败"
    assert strategy.active, "策略活跃状态错误"
    
    # 测试参数
    assert strategy.get_parameter('param1') == 'value1', "参数获取错误"
    strategy.set_parameter('param2', 'value2')
    assert strategy.get_parameter('param2') == 'value2', "参数设置错误"
    
    # 测试K线处理
    bar = BarData(
        symbol='TEST',
        datetime=datetime.now(),
        interval=Interval.MINUTE_1,
        open_price=Decimal('100.00'),
        high_price=Decimal('101.00'),
        low_price=Decimal('99.50'),
        close_price=Decimal('100.50'),
        volume=1000,
        turnover=Decimal('100500.00')
    )
    
    strategy.process_bar(bar)
    
    # 检查数据缓存
    bars = strategy.get_bars('TEST')
    assert len(bars) == 1, "K线数据缓存错误"
    
    # 测试停止
    assert strategy.stop(), "策略停止失败"
    assert not strategy.active, "策略停止状态错误"
    
    print("✓ 策略基类测试通过")


def test_ma_cross_strategy():
    """测试移动平均交叉策略"""
    print("测试移动平均交叉策略...")
    
    # 创建策略
    parameters = {
        'fast_period': 5,
        'slow_period': 10,
        'symbol': 'AAPL',
        'volume': 100
    }
    
    strategy = MACrossStrategy('ma_cross_test', parameters)
    
    # 初始化和启动
    assert strategy.initialize(), "MA策略初始化失败"
    assert strategy.start(), "MA策略启动失败"
    
    # 模拟K线数据
    base_time = datetime(2024, 1, 2, 9, 30)
    
    # 生成上涨趋势的K线数据
    for i in range(20):
        bar = BarData(
            symbol='AAPL',
            datetime=base_time.replace(minute=30+i),
            interval=Interval.MINUTE_1,
            open_price=Decimal(f'{100 + i * 0.5:.2f}'),
            high_price=Decimal(f'{100.5 + i * 0.5:.2f}'),
            low_price=Decimal(f'{99.5 + i * 0.5:.2f}'),
            close_price=Decimal(f'{100.2 + i * 0.5:.2f}'),
            volume=1000,
            turnover=Decimal(f'{100200 + i * 500:.2f}')
        )
        strategy.process_bar(bar)
    
    # 检查指标
    indicators = strategy.get_current_indicators()
    print(f"当前指标: {indicators}")
    
    # 检查统计信息
    stats = strategy.get_statistics()
    print(f"策略统计: {stats}")
    
    assert strategy.stop(), "MA策略停止失败"
    
    print("✓ 移动平均交叉策略测试通过")


def test_strategy_manager():
    """测试策略管理器"""
    print("测试策略管理器...")
    
    manager = StrategyManager()
    
    # 加载策略
    assert manager.load_strategy(TestStrategy, 'test1', {'param': 'value1'}), "加载策略失败"
    assert manager.load_strategy(TestStrategy, 'test2', {'param': 'value2'}), "加载策略失败"
    
    # 检查策略列表
    strategies = manager.get_all_strategies()
    assert len(strategies) == 2, f"策略数量错误: {len(strategies)}"
    
    # 启动策略
    assert manager.start_strategy('test1'), "启动策略失败"
    assert manager.start_strategy('test2'), "启动策略失败"
    
    # 检查策略状态
    strategy1 = manager.get_strategy('test1')
    assert strategy1 and strategy1.active, "策略1状态错误"
    
    strategy2 = manager.get_strategy('test2')
    assert strategy2 and strategy2.active, "策略2状态错误"
    
    # 停止策略
    assert manager.stop_strategy('test1'), "停止策略失败"
    assert not strategy1.active, "策略1停止状态错误"
    
    # 移除策略
    assert manager.remove_strategy('test1'), "移除策略失败"
    assert 'test1' not in manager.strategies, "策略移除失败"
    
    print("✓ 策略管理器测试通过")


def test_backtest_engine():
    """测试回测引擎"""
    print("测试回测引擎...")
    
    # 创建回测引擎
    engine = BacktestEngine(initial_capital=Decimal('100000'))
    
    # 创建测试策略
    strategy = MACrossStrategy('ma_backtest', {
        'fast_period': 5,
        'slow_period': 10,
        'symbol': 'TEST',
        'volume': 100
    })
    
    # 添加策略
    assert engine.add_strategy(strategy), "添加策略失败"
    
    # 生成测试数据
    test_bars = []
    base_time = datetime(2024, 1, 2, 9, 30)
    
    for i in range(50):
        # 生成有趋势的价格数据
        base_price = 100 + i * 0.1 + (i % 10) * 0.05

        # 使用timedelta来计算时间
        from datetime import timedelta
        bar_time = base_time + timedelta(minutes=i)

        bar = BarData(
            symbol='TEST',
            datetime=bar_time,
            interval=Interval.MINUTE_1,
            open_price=Decimal(f'{base_price:.2f}'),
            high_price=Decimal(f'{base_price + 0.5:.2f}'),
            low_price=Decimal(f'{base_price - 0.3:.2f}'),
            close_price=Decimal(f'{base_price + 0.2:.2f}'),
            volume=1000,
            turnover=Decimal(f'{(base_price + 0.2) * 1000:.2f}')
        )
        test_bars.append(bar)
    
    # 加载数据
    engine.load_data('TEST', test_bars)
    
    # 运行回测
    start_date = date(2024, 1, 2)
    end_date = date(2024, 1, 2)
    
    result = engine.run_backtest(start_date, end_date)
    
    # 检查回测结果
    assert 'total_return' in result, "回测结果缺少总收益率"
    assert 'total_trades' in result, "回测结果缺少交易次数"
    assert 'final_balance' in result, "回测结果缺少最终余额"
    
    print(f"回测结果:")
    print(f"  初始资金: {result['initial_capital']}")
    print(f"  最终余额: {result['final_balance']}")
    print(f"  总收益率: {result['total_return']:.2%}")
    print(f"  交易次数: {result['total_trades']}")
    print(f"  胜率: {result['win_rate']:.2%}")
    print(f"  最大回撤: {result['max_drawdown']:.2%}")
    
    print("✓ 回测引擎测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行策略引擎测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger(
        name='test_strategy_engine',
        level='INFO',
        enable_console=True,
        enable_events=False
    )
    
    try:
        test_base_strategy()
        test_ma_cross_strategy()
        test_strategy_manager()
        test_backtest_engine()
        
        print("=" * 50)
        print("✓ 所有策略引擎测试通过！")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
