"""
虚拟币列表 ViewModel
负责虚拟币数据的获取、管理和业务逻辑
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
from datetime import datetime
from decimal import Decimal

from PySide6.QtCore import QObject, Signal, QTimer, QThread, pyqtSignal

from core.event_types import TickEvent
from data_service.connectors.binance_connector import BinanceConnector


class CryptoDataWorker(QThread):
    """虚拟币数据获取工作线程"""
    
    # 信号定义
    symbols_loaded = pyqtSignal(list)  # 品种列表加载完成
    tickers_loaded = pyqtSignal(list)  # 价格数据加载完成
    error_occurred = pyqtSignal(str)   # 错误发生
    
    def __init__(self, connector: BinanceConnector):
        super().__init__()
        self.connector = connector
        self.running = False
        
    def run(self):
        """运行数据获取"""
        self.running = True
        
        try:
            # 获取所有交易品种
            symbols = self.connector.get_all_symbols()
            if symbols:
                # 过滤USDT交易对
                usdt_symbols = [s for s in symbols if s['quoteAsset'] == 'USDT']
                self.symbols_loaded.emit(usdt_symbols)
            
            # 获取24小时价格统计
            tickers = self.connector.get_24hr_ticker()
            if tickers:
                # 过滤USDT交易对
                usdt_tickers = [t for t in tickers if t['symbol'].endswith('USDT')]
                self.tickers_loaded.emit(usdt_tickers)
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def stop(self):
        """停止工作线程"""
        self.running = False
        self.quit()
        self.wait()


class CryptoListViewModel(QObject):
    """
    虚拟币列表ViewModel
    
    职责:
    1. 管理Binance连接器
    2. 获取虚拟币品种列表
    3. 获取实时价格数据
    4. 提供数据更新信号
    """
    
    # 信号定义
    symbols_updated = Signal(list)      # 品种列表更新
    prices_updated = Signal(list)       # 价格数据更新
    connection_status = Signal(bool, str)  # 连接状态
    error_occurred = Signal(str)        # 错误发生
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        super().__init__()
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Binance连接器
        self.connector: Optional[BinanceConnector] = None
        
        # 数据缓存
        self.symbols_cache: List[Dict[str, Any]] = []
        self.tickers_cache: List[Dict[str, Any]] = []
        self.latest_prices: Dict[str, Dict[str, Any]] = {}
        
        # 工作线程
        self.data_worker: Optional[CryptoDataWorker] = None
        
        # 定时器用于定期更新价格
        self.price_update_timer = QTimer()
        self.price_update_timer.timeout.connect(self._update_prices)
        self.price_update_interval = 5000  # 5秒更新一次
        
        # 初始化连接器
        self._init_connector()
        
    def _init_connector(self) -> None:
        """初始化Binance连接器"""
        try:
            # 从配置中获取Binance设置
            binance_config = self.config.get('data_service', {}).get('connectors', {}).get('binance', {})
            
            if not binance_config.get('enabled', False):
                self.logger.warning("Binance连接器未启用")
                return
            
            # 创建连接器
            self.connector = BinanceConnector('binance', binance_config)
            
            # 连接到Binance
            if self.connector.connect():
                self.connection_status.emit(True, "成功连接到Binance")
                self.logger.info("Binance连接器初始化成功")
                
                # 开始加载数据
                self._load_initial_data()
                
                # 启动价格更新定时器
                self.price_update_timer.start(self.price_update_interval)
                
            else:
                self.connection_status.emit(False, "连接Binance失败")
                self.logger.error("Binance连接器连接失败")
                
        except Exception as e:
            self.logger.error(f"初始化Binance连接器失败: {e}")
            self.connection_status.emit(False, f"初始化失败: {e}")
            self.error_occurred.emit(f"初始化Binance连接器失败: {e}")
    
    def _load_initial_data(self) -> None:
        """加载初始数据"""
        try:
            if not self.connector:
                return
            
            # 创建工作线程
            self.data_worker = CryptoDataWorker(self.connector)
            self.data_worker.symbols_loaded.connect(self._on_symbols_loaded)
            self.data_worker.tickers_loaded.connect(self._on_tickers_loaded)
            self.data_worker.error_occurred.connect(self._on_worker_error)
            
            # 启动工作线程
            self.data_worker.start()
            
        except Exception as e:
            self.logger.error(f"加载初始数据失败: {e}")
            self.error_occurred.emit(f"加载初始数据失败: {e}")
    
    def _on_symbols_loaded(self, symbols: List[Dict[str, Any]]) -> None:
        """处理品种列表加载完成"""
        try:
            self.symbols_cache = symbols
            self.symbols_updated.emit(symbols)
            self.logger.info(f"加载了{len(symbols)}个USDT交易对")
            
        except Exception as e:
            self.logger.error(f"处理品种列表失败: {e}")
    
    def _on_tickers_loaded(self, tickers: List[Dict[str, Any]]) -> None:
        """处理价格数据加载完成"""
        try:
            self.tickers_cache = tickers
            
            # 更新价格缓存
            for ticker in tickers:
                symbol = ticker['symbol']
                self.latest_prices[symbol] = {
                    'price': Decimal(ticker['lastPrice']),
                    'change': Decimal(ticker['priceChange']),
                    'change_percent': Decimal(ticker['priceChangePercent']),
                    'volume': Decimal(ticker['volume']),
                    'high': Decimal(ticker['highPrice']),
                    'low': Decimal(ticker['lowPrice']),
                    'open': Decimal(ticker['openPrice']),
                    'count': int(ticker['count'])
                }
            
            # 合并品种和价格数据
            combined_data = self._combine_symbols_and_prices()
            self.prices_updated.emit(combined_data)
            
            self.logger.info(f"更新了{len(tickers)}个品种的价格数据")
            
        except Exception as e:
            self.logger.error(f"处理价格数据失败: {e}")
    
    def _on_worker_error(self, error_msg: str) -> None:
        """处理工作线程错误"""
        self.logger.error(f"数据获取线程错误: {error_msg}")
        self.error_occurred.emit(error_msg)
    
    def _update_prices(self) -> None:
        """定期更新价格数据"""
        try:
            if not self.connector or not self.connector.is_connected():
                return
            
            # 在工作线程中更新价格
            if self.data_worker and not self.data_worker.isRunning():
                self.data_worker = CryptoDataWorker(self.connector)
                self.data_worker.tickers_loaded.connect(self._on_tickers_loaded)
                self.data_worker.error_occurred.connect(self._on_worker_error)
                self.data_worker.start()
                
        except Exception as e:
            self.logger.error(f"更新价格失败: {e}")
    
    def _combine_symbols_and_prices(self) -> List[Dict[str, Any]]:
        """合并品种信息和价格数据"""
        combined_data = []
        
        try:
            # 创建品种信息字典
            symbols_dict = {s['symbol']: s for s in self.symbols_cache}
            
            # 合并数据
            for symbol, price_data in self.latest_prices.items():
                if symbol in symbols_dict:
                    symbol_info = symbols_dict[symbol]
                    
                    combined_item = {
                        'symbol': symbol,
                        'baseAsset': symbol_info['baseAsset'],
                        'quoteAsset': symbol_info['quoteAsset'],
                        'price': price_data['price'],
                        'change': price_data['change'],
                        'change_percent': price_data['change_percent'],
                        'volume': price_data['volume'],
                        'high': price_data['high'],
                        'low': price_data['low'],
                        'open': price_data['open'],
                        'count': price_data['count']
                    }
                    
                    combined_data.append(combined_item)
            
            # 按成交量排序
            combined_data.sort(key=lambda x: float(x['volume']), reverse=True)
            
        except Exception as e:
            self.logger.error(f"合并数据失败: {e}")
        
        return combined_data
    
    def get_symbol_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取指定品种的数据"""
        return self.latest_prices.get(symbol)
    
    def refresh_data(self) -> None:
        """刷新数据"""
        try:
            if self.connector and self.connector.is_connected():
                self._load_initial_data()
            else:
                self._init_connector()
                
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            self.error_occurred.emit(f"刷新数据失败: {e}")
    
    def disconnect(self) -> None:
        """断开连接"""
        try:
            # 停止定时器
            if self.price_update_timer.isActive():
                self.price_update_timer.stop()
            
            # 停止工作线程
            if self.data_worker:
                self.data_worker.stop()
                self.data_worker = None
            
            # 断开连接器
            if self.connector:
                self.connector.disconnect()
                self.connector = None
            
            # 清理缓存
            self.symbols_cache.clear()
            self.tickers_cache.clear()
            self.latest_prices.clear()
            
            self.connection_status.emit(False, "已断开连接")
            self.logger.info("虚拟币数据连接已断开")
            
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
    
    def get_top_symbols(self, count: int = 50) -> List[Dict[str, Any]]:
        """获取成交量前N的品种"""
        combined_data = self._combine_symbols_and_prices()
        return combined_data[:count]
