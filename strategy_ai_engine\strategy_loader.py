"""
策略加载器
动态加载和管理用户策略
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List, Type
import logging
import importlib
import importlib.util
import inspect
import os
import sys
from pathlib import Path

from strategies.base_strategy import BaseStrategy


class StrategyInfo:
    """策略信息"""
    
    def __init__(
        self,
        name: str,
        class_name: str,
        file_path: str,
        description: str = "",
        version: str = "1.0",
        author: str = "",
        parameters: Dict[str, Any] = None
    ):
        self.name = name
        self.class_name = class_name
        self.file_path = file_path
        self.description = description
        self.version = version
        self.author = author
        self.parameters = parameters or {}


class StrategyLoader:
    """
    策略加载器
    
    职责:
    1. 扫描策略目录
    2. 动态加载策略类
    3. 验证策略有效性
    4. 管理策略生命周期
    5. 提供策略信息
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 策略配置
        self.strategy_config = config.get('strategy_engine', {})
        self.strategy_path = self.strategy_config.get('strategy_path', './strategies')
        self.max_strategies = self.strategy_config.get('max_strategies', 10)
        
        # 策略注册表
        self.loaded_strategies: Dict[str, Type[BaseStrategy]] = {}
        self.strategy_info: Dict[str, StrategyInfo] = {}
        self.strategy_instances: Dict[str, BaseStrategy] = {}
        
        # 确保策略路径存在
        self._ensure_strategy_path()
        
        # 初始加载策略
        self._load_all_strategies()
    
    def _ensure_strategy_path(self) -> None:
        """确保策略路径存在"""
        try:
            strategy_dir = Path(self.strategy_path)
            if not strategy_dir.exists():
                strategy_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"创建策略目录: {strategy_dir}")
            
            # 确保策略路径在Python路径中
            if str(strategy_dir.parent) not in sys.path:
                sys.path.insert(0, str(strategy_dir.parent))
                
        except Exception as e:
            self.logger.error(f"设置策略路径失败: {e}")
    
    def _load_all_strategies(self) -> None:
        """加载所有策略"""
        try:
            strategy_dir = Path(self.strategy_path)
            if not strategy_dir.exists():
                self.logger.warning(f"策略目录不存在: {strategy_dir}")
                return
            
            # 扫描Python文件
            for py_file in strategy_dir.glob("*.py"):
                if py_file.name.startswith("__") or py_file.name == "base_strategy.py":
                    continue
                
                try:
                    self._load_strategy_from_file(py_file)
                except Exception as e:
                    self.logger.error(f"加载策略文件 {py_file} 失败: {e}")
            
            self.logger.info(f"已加载 {len(self.loaded_strategies)} 个策略")
            
        except Exception as e:
            self.logger.error(f"加载策略失败: {e}")
    
    def _load_strategy_from_file(self, file_path: Path) -> None:
        """从文件加载策略"""
        try:
            # 构建模块名
            module_name = f"strategies.{file_path.stem}"
            
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec is None or spec.loader is None:
                self.logger.error(f"无法创建模块规范: {file_path}")
                return
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找策略类
            strategy_classes = self._find_strategy_classes(module)
            
            for strategy_class in strategy_classes:
                self._register_strategy(strategy_class, str(file_path))
                
        except Exception as e:
            self.logger.error(f"从文件 {file_path} 加载策略失败: {e}")
    
    def _find_strategy_classes(self, module: Any) -> List[Type[BaseStrategy]]:
        """在模块中查找策略类"""
        strategy_classes = []
        
        try:
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # 检查是否是BaseStrategy的子类（但不是BaseStrategy本身）
                if (issubclass(obj, BaseStrategy) and 
                    obj != BaseStrategy and 
                    obj.__module__ == module.__name__):
                    strategy_classes.append(obj)
                    
        except Exception as e:
            self.logger.error(f"查找策略类失败: {e}")
        
        return strategy_classes
    
    def _register_strategy(self, strategy_class: Type[BaseStrategy], file_path: str) -> None:
        """注册策略"""
        try:
            strategy_name = strategy_class.__name__
            
            # 验证策略
            if not self._validate_strategy(strategy_class):
                self.logger.warning(f"策略 {strategy_name} 验证失败，跳过注册")
                return
            
            # 获取策略信息
            strategy_info = self._extract_strategy_info(strategy_class, file_path)
            
            # 注册策略
            self.loaded_strategies[strategy_name] = strategy_class
            self.strategy_info[strategy_name] = strategy_info
            
            self.logger.info(f"注册策略: {strategy_name}")
            
        except Exception as e:
            self.logger.error(f"注册策略失败: {e}")
    
    def _validate_strategy(self, strategy_class: Type[BaseStrategy]) -> bool:
        """验证策略类"""
        try:
            # 检查必需的方法
            required_methods = ['on_init', 'on_start', 'on_bar', 'on_stop']
            
            for method_name in required_methods:
                if not hasattr(strategy_class, method_name):
                    self.logger.error(f"策略 {strategy_class.__name__} 缺少方法: {method_name}")
                    return False
                
                method = getattr(strategy_class, method_name)
                if not callable(method):
                    self.logger.error(f"策略 {strategy_class.__name__} 的 {method_name} 不是可调用对象")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证策略失败: {e}")
            return False
    
    def _extract_strategy_info(self, strategy_class: Type[BaseStrategy], file_path: str) -> StrategyInfo:
        """提取策略信息"""
        try:
            # 从类文档字符串或属性获取信息
            description = strategy_class.__doc__ or ""
            version = getattr(strategy_class, '__version__', '1.0')
            author = getattr(strategy_class, '__author__', '')
            
            # 获取参数信息
            parameters = {}
            if hasattr(strategy_class, 'get_parameters'):
                try:
                    parameters = strategy_class.get_parameters()
                except:
                    pass
            
            return StrategyInfo(
                name=strategy_class.__name__,
                class_name=strategy_class.__name__,
                file_path=file_path,
                description=description.strip(),
                version=version,
                author=author,
                parameters=parameters
            )
            
        except Exception as e:
            self.logger.error(f"提取策略信息失败: {e}")
            return StrategyInfo(
                name=strategy_class.__name__,
                class_name=strategy_class.__name__,
                file_path=file_path
            )
    
    def get_strategy_class(self, strategy_name: str) -> Optional[Type[BaseStrategy]]:
        """获取策略类"""
        return self.loaded_strategies.get(strategy_name)
    
    def get_strategy_info(self, strategy_name: str) -> Optional[StrategyInfo]:
        """获取策略信息"""
        return self.strategy_info.get(strategy_name)
    
    def get_all_strategies(self) -> Dict[str, StrategyInfo]:
        """获取所有策略信息"""
        return self.strategy_info.copy()
    
    def create_strategy_instance(
        self, 
        strategy_name: str, 
        config: Dict[str, Any],
        instance_name: Optional[str] = None
    ) -> Optional[BaseStrategy]:
        """
        创建策略实例
        
        Args:
            strategy_name: 策略名称
            config: 策略配置
            instance_name: 实例名称
            
        Returns:
            BaseStrategy: 策略实例
        """
        try:
            strategy_class = self.get_strategy_class(strategy_name)
            if strategy_class is None:
                self.logger.error(f"策略 {strategy_name} 未找到")
                return None
            
            # 创建实例
            instance = strategy_class(config)
            
            # 注册实例
            if instance_name is None:
                instance_name = f"{strategy_name}_{len(self.strategy_instances)}"
            
            self.strategy_instances[instance_name] = instance
            
            self.logger.info(f"创建策略实例: {instance_name}")
            return instance
            
        except Exception as e:
            self.logger.error(f"创建策略实例失败: {e}")
            return None
    
    def remove_strategy_instance(self, instance_name: str) -> bool:
        """移除策略实例"""
        try:
            if instance_name in self.strategy_instances:
                instance = self.strategy_instances[instance_name]
                
                # 停止策略
                if hasattr(instance, 'stop'):
                    instance.stop()
                
                # 移除实例
                del self.strategy_instances[instance_name]
                
                self.logger.info(f"移除策略实例: {instance_name}")
                return True
            else:
                self.logger.warning(f"策略实例 {instance_name} 不存在")
                return False
                
        except Exception as e:
            self.logger.error(f"移除策略实例失败: {e}")
            return False
    
    def get_strategy_instance(self, instance_name: str) -> Optional[BaseStrategy]:
        """获取策略实例"""
        return self.strategy_instances.get(instance_name)
    
    def get_all_instances(self) -> Dict[str, BaseStrategy]:
        """获取所有策略实例"""
        return self.strategy_instances.copy()
    
    def reload_strategy(self, strategy_name: str) -> bool:
        """重新加载策略"""
        try:
            # 获取策略信息
            strategy_info = self.get_strategy_info(strategy_name)
            if strategy_info is None:
                self.logger.error(f"策略 {strategy_name} 信息未找到")
                return False
            
            # 移除旧的策略
            if strategy_name in self.loaded_strategies:
                del self.loaded_strategies[strategy_name]
            if strategy_name in self.strategy_info:
                del self.strategy_info[strategy_name]
            
            # 重新加载
            file_path = Path(strategy_info.file_path)
            self._load_strategy_from_file(file_path)
            
            self.logger.info(f"重新加载策略: {strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"重新加载策略失败: {e}")
            return False
    
    def reload_all_strategies(self) -> None:
        """重新加载所有策略"""
        try:
            # 清空现有策略
            self.loaded_strategies.clear()
            self.strategy_info.clear()
            
            # 重新加载
            self._load_all_strategies()
            
            self.logger.info("重新加载所有策略完成")
            
        except Exception as e:
            self.logger.error(f"重新加载所有策略失败: {e}")
    
    def get_strategy_count(self) -> int:
        """获取策略数量"""
        return len(self.loaded_strategies)
    
    def get_instance_count(self) -> int:
        """获取实例数量"""
        return len(self.strategy_instances)
