#!/usr/bin/env python3
"""
简单GUI测试
测试基本的GUI组件是否工作
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
    from PySide6.QtCore import QTimer
    import pyqtgraph as pg
    
    class SimpleTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Gemini Quant - 简单测试")
            self.setGeometry(100, 100, 800, 600)
            
            # 中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # 标签
            label = QLabel("🚀 Gemini Quant GUI测试成功！")
            label.setStyleSheet("font-size: 24px; color: green; font-weight: bold;")
            layout.addWidget(label)
            
            # 简单图表
            plot_widget = pg.PlotWidget(title="测试图表")
            plot_widget.plot([1, 2, 3, 4, 5], [1, 4, 2, 5, 3], pen='r', symbol='o')
            layout.addWidget(plot_widget)
            
            # 定时器关闭窗口
            self.timer = QTimer()
            self.timer.timeout.connect(self.close)
            self.timer.start(5000)  # 5秒后关闭
            
            print("✅ GUI窗口创建成功，5秒后自动关闭")
    
    def main():
        print("🧪 开始简单GUI测试...")
        
        app = QApplication(sys.argv)
        window = SimpleTestWindow()
        window.show()
        
        print("📊 GUI窗口已显示")
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"❌ GUI库导入失败: {e}")
    print("💡 请安装: pip install PySide6 pyqtgraph")
    sys.exit(1)
