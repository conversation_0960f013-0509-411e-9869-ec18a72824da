"""
投资组合 ViewModel
负责投资组合数据的管理和展示
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
from decimal import Decimal

from PySide6.QtCore import QObject, Signal

from core.event_types import PositionUpdateEvent, AccountUpdateEvent, OrderEvent, TradeEvent
from core.data_types import PositionData, AccountData, OrderData, TradeData


class PortfolioViewModel(QObject):
    """
    投资组合ViewModel
    
    职责:
    1. 订阅投资组合相关事件
    2. 管理账户、持仓、订单、成交数据
    3. 提供数据更新信号
    4. 计算投资组合统计信息
    """
    
    # 信号定义
    account_updated = Signal(object)  # AccountData
    positions_updated = Signal(list)  # List[PositionData]
    orders_updated = Signal(list)  # List[OrderData]
    trades_updated = Signal(list)  # List[TradeData]
    portfolio_stats_updated = Signal(dict)  # 投资组合统计
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        super().__init__()
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.account: Optional[AccountData] = None
        self.positions: List[PositionData] = []
        self.orders: List[OrderData] = []
        self.trades: List[TradeData] = []
        
        # 配置
        self.max_orders_display = 100
        self.max_trades_display = 100
        
        # 订阅事件
        self._subscribe_events()
        
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("account_update", self._on_account_event, "portfolio_vm_account")
            self.event_bus.subscribe("position_update", self._on_position_event, "portfolio_vm_position")
            self.event_bus.subscribe("order", self._on_order_event, "portfolio_vm_order")
            self.event_bus.subscribe("trade", self._on_trade_event, "portfolio_vm_trade")
    
    def _on_account_event(self, event: AccountUpdateEvent) -> None:
        """处理账户更新事件"""
        try:
            self.account = event.data
            self.account_updated.emit(self.account)
            
            # 更新投资组合统计
            self._update_portfolio_stats()
            
            self.logger.debug(f"账户更新: 余额={self.account.balance}")
            
        except Exception as e:
            self.logger.error(f"处理账户事件失败: {e}")
    
    def _on_position_event(self, event: PositionUpdateEvent) -> None:
        """处理持仓更新事件"""
        try:
            new_position = event.data
            
            # 查找并更新现有持仓
            updated = False
            for i, position in enumerate(self.positions):
                if (position.symbol == new_position.symbol and 
                    position.direction == new_position.direction):
                    
                    if new_position.volume == 0:
                        # 删除空持仓
                        self.positions.pop(i)
                    else:
                        # 更新持仓
                        self.positions[i] = new_position
                    updated = True
                    break
            
            # 如果是新持仓且数量大于0
            if not updated and new_position.volume > 0:
                self.positions.append(new_position)
            
            self.positions_updated.emit(self.positions.copy())
            
            # 更新投资组合统计
            self._update_portfolio_stats()
            
            self.logger.debug(f"持仓更新: {new_position.symbol} {new_position.direction.value} {new_position.volume}")
            
        except Exception as e:
            self.logger.error(f"处理持仓事件失败: {e}")
    
    def _on_order_event(self, event: OrderEvent) -> None:
        """处理订单事件"""
        try:
            new_order = event.data
            
            # 查找并更新现有订单
            updated = False
            for i, order in enumerate(self.orders):
                if order.order_id == new_order.order_id:
                    self.orders[i] = new_order
                    updated = True
                    break
            
            # 如果是新订单
            if not updated:
                self.orders.append(new_order)
                
                # 限制订单数量
                if len(self.orders) > self.max_orders_display:
                    self.orders = self.orders[-self.max_orders_display:]
            
            self.orders_updated.emit(self.orders.copy())
            
            self.logger.debug(f"订单更新: {new_order.order_id} {new_order.status.value}")
            
        except Exception as e:
            self.logger.error(f"处理订单事件失败: {e}")
    
    def _on_trade_event(self, event: TradeEvent) -> None:
        """处理成交事件"""
        try:
            new_trade = event.data
            self.trades.append(new_trade)
            
            # 限制成交记录数量
            if len(self.trades) > self.max_trades_display:
                self.trades = self.trades[-self.max_trades_display:]
            
            self.trades_updated.emit(self.trades.copy())
            
            self.logger.debug(f"成交更新: {new_trade.trade_id} {new_trade.symbol} {new_trade.volume}")
            
        except Exception as e:
            self.logger.error(f"处理成交事件失败: {e}")
    
    def _update_portfolio_stats(self) -> None:
        """更新投资组合统计"""
        try:
            stats = {}
            
            if self.account:
                stats['total_balance'] = float(self.account.balance)
                stats['available'] = float(self.account.available)
                stats['frozen'] = float(self.account.frozen)
                stats['margin_used'] = float(self.account.frozen)
                stats['margin_ratio'] = float(self.account.frozen / self.account.balance) if self.account.balance > 0 else 0
            
            # 持仓统计
            stats['total_positions'] = len(self.positions)
            stats['long_positions'] = len([p for p in self.positions if p.direction.value == 'LONG'])
            stats['short_positions'] = len([p for p in self.positions if p.direction.value == 'SHORT'])
            
            # 计算总盈亏
            total_pnl = sum(float(p.pnl) for p in self.positions)
            stats['total_pnl'] = total_pnl
            
            # 计算总市值
            total_market_value = sum(float(p.price * p.volume) for p in self.positions)
            stats['total_market_value'] = total_market_value
            
            # 今日订单统计
            from datetime import datetime, date
            today = date.today()
            today_orders = [o for o in self.orders if o.datetime.date() == today]
            stats['today_orders'] = len(today_orders)
            
            # 今日成交统计
            today_trades = [t for t in self.trades if t.datetime.date() == today]
            stats['today_trades'] = len(today_trades)
            stats['today_volume'] = sum(t.volume for t in today_trades)
            stats['today_turnover'] = sum(float(t.price * t.volume) for t in today_trades)
            
            self.portfolio_stats_updated.emit(stats)
            
        except Exception as e:
            self.logger.error(f"更新投资组合统计失败: {e}")
    
    def get_position_by_symbol(self, symbol: str) -> List[PositionData]:
        """根据品种获取持仓"""
        return [p for p in self.positions if p.symbol == symbol]
    
    def get_total_pnl(self) -> Decimal:
        """获取总盈亏"""
        return sum(p.pnl for p in self.positions)
    
    def get_account_info(self) -> Optional[AccountData]:
        """获取账户信息"""
        return self.account
    
    def get_recent_orders(self, count: int = 10) -> List[OrderData]:
        """获取最近订单"""
        return self.orders[-count:] if len(self.orders) >= count else self.orders
    
    def get_recent_trades(self, count: int = 10) -> List[TradeData]:
        """获取最近成交"""
        return self.trades[-count:] if len(self.trades) >= count else self.trades
