"""
数据连接器基类
使用适配器模式实现统一的数据接入接口
"""

from __future__ import annotations
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import logging

from core.data_types import TickData, BarData, SymbolInfo
from core.event_types import BaseEvent


class BaseConnector(ABC):
    """
    数据连接器基类
    
    所有数据源连接器都必须继承此类并实现抽象方法
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self._connected = False
        self._subscribed_symbols: List[str] = []
        self._event_callback: Optional[Callable[[BaseEvent], None]] = None
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到数据源
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开数据源连接
        
        Returns:
            bool: 断开是否成功
        """
        pass
    
    @abstractmethod
    def subscribe(self, symbol: str) -> bool:
        """
        订阅指定品种的数据
        
        Args:
            symbol: 品种代码
            
        Returns:
            bool: 订阅是否成功
        """
        pass
    
    @abstractmethod
    def unsubscribe(self, symbol: str) -> bool:
        """
        取消订阅指定品种的数据
        
        Args:
            symbol: 品种代码
            
        Returns:
            bool: 取消订阅是否成功
        """
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Optional[SymbolInfo]:
        """
        获取品种信息
        
        Args:
            symbol: 品种代码
            
        Returns:
            SymbolInfo: 品种信息，如果不存在返回None
        """
        pass
    
    @abstractmethod
    def get_historical_bars(
        self, 
        symbol: str, 
        start_time: datetime, 
        end_time: datetime,
        interval: str = "1m"
    ) -> List[BarData]:
        """
        获取历史K线数据
        
        Args:
            symbol: 品种代码
            start_time: 开始时间
            end_time: 结束时间
            interval: 时间间隔
            
        Returns:
            List[BarData]: K线数据列表
        """
        pass
    
    def set_event_callback(self, callback: Callable[[BaseEvent], None]) -> None:
        """设置事件回调函数"""
        self._event_callback = callback
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    def get_subscribed_symbols(self) -> List[str]:
        """获取已订阅的品种列表"""
        return self._subscribed_symbols.copy()
    
    def _on_tick_data(self, tick: TickData) -> None:
        """处理Tick数据"""
        if self._event_callback:
            from core.event_types import TickEvent
            event = TickEvent(data=tick, source=self.name)
            self._event_callback(event)
    
    def _on_bar_data(self, bar: BarData) -> None:
        """处理K线数据"""
        if self._event_callback:
            from core.event_types import BarEvent
            event = BarEvent(data=bar, source=self.name)
            self._event_callback(event)
    
    def _on_connection_status(self, connected: bool, message: str = "") -> None:
        """处理连接状态变化"""
        self._connected = connected
        if self._event_callback:
            from core.event_types import DataServiceStatusEvent
            status = "connected" if connected else "disconnected"
            event = DataServiceStatusEvent(
                service_name=self.name,
                status=status,
                message=message,
                source=self.name
            )
            self._event_callback(event)
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.name})"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}', connected={self._connected})"
