"""
虚拟币列表视图
显示从Binance获取的虚拟币交易品种列表
"""

from __future__ import annotations
from typing import Any, Optional, List, Dict
import logging

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QLabel, QLineEdit, QComboBox, QProgressBar,
    QMessageBox, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor, QFont
from decimal import Decimal

from gui.viewmodels.crypto_list_vm import CryptoListViewModel


class CryptoListView(QWidget):
    """
    虚拟币列表视图
    
    功能:
    1. 显示虚拟币交易品种列表
    2. 显示实时价格和涨跌幅
    3. 支持搜索和排序
    4. 点击品种显示K线图
    """
    
    # 信号定义
    symbol_selected = Signal(str)  # 品种被选中
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[CryptoListViewModel] = None
        
        # UI组件
        self.table: Optional[QTableWidget] = None
        self.search_edit: Optional[QLineEdit] = None
        self.sort_combo: Optional[QComboBox] = None
        self.refresh_btn: Optional[QPushButton] = None
        self.status_label: Optional[QLabel] = None
        self.progress_bar: Optional[QProgressBar] = None
        
        # 数据缓存
        self.crypto_data: List[Dict[str, Any]] = []
        self.filtered_data: List[Dict[str, Any]] = []
        
        # 表格列定义
        self.columns = [
            ('品种', 'symbol', 100),
            ('名称', 'baseAsset', 80),
            ('价格(USDT)', 'price', 120),
            ('24h涨跌', 'change', 100),
            ('24h涨跌%', 'change_percent', 100),
            ('24h成交量', 'volume', 150),
            ('24h最高', 'high', 100),
            ('24h最低', 'low', 100)
        ]
        
        # 初始化UI
        self._init_ui()
        
        # 定时刷新显示
        self.display_timer = QTimer()
        self.display_timer.timeout.connect(self._update_display)
        self.display_timer.start(1000)  # 每秒更新一次显示
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = self._create_control_panel()
        layout.addLayout(control_layout)
        
        # 状态栏
        status_layout = self._create_status_bar()
        layout.addLayout(status_layout)
        
        # 数据表格
        self.table = self._create_table()
        layout.addWidget(self.table)
        
        # 设置样式
        self._setup_styles()
        
    def _create_control_panel(self) -> QHBoxLayout:
        """创建控制面板"""
        layout = QHBoxLayout()
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入品种名称...")
        self.search_edit.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_edit)
        
        # 排序选择
        layout.addWidget(QLabel("排序:"))
        self.sort_combo = QComboBox()
        self.sort_combo.addItems([
            "成交量(降序)", "涨跌幅(降序)", "涨跌幅(升序)", 
            "价格(降序)", "价格(升序)", "品种名称"
        ])
        self.sort_combo.currentTextChanged.connect(self._on_sort_changed)
        layout.addWidget(self.sort_combo)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self._on_refresh_clicked)
        layout.addWidget(self.refresh_btn)
        
        layout.addStretch()
        return layout
        
    def _create_status_bar(self) -> QHBoxLayout:
        """创建状态栏"""
        layout = QHBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("未连接")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        return layout
        
    def _create_table(self) -> QTableWidget:
        """创建数据表格"""
        table = QTableWidget()
        
        # 设置列
        table.setColumnCount(len(self.columns))
        headers = [col[0] for col in self.columns]
        table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        header = table.horizontalHeader()
        for i, (_, _, width) in enumerate(self.columns):
            table.setColumnWidth(i, width)
        
        # 设置表格属性
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setAlternatingRowColors(True)
        table.setSortingEnabled(True)
        
        # 连接信号
        table.cellClicked.connect(self._on_cell_clicked)
        table.cellDoubleClicked.connect(self._on_cell_double_clicked)
        
        return table
        
    def _setup_styles(self) -> None:
        """设置样式"""
        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: #ffffff;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
        """)
        
    def set_view_model(self, view_model: CryptoListViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接信号
        self.view_model.symbols_updated.connect(self._on_symbols_updated)
        self.view_model.prices_updated.connect(self._on_prices_updated)
        self.view_model.connection_status.connect(self._on_connection_status)
        self.view_model.error_occurred.connect(self._on_error_occurred)
        
        self.logger.info("虚拟币列表ViewModel已连接")
        
    def _on_symbols_updated(self, symbols: List[Dict[str, Any]]) -> None:
        """处理品种列表更新"""
        try:
            self.logger.info(f"收到品种列表更新: {len(symbols)}个品种")
            
        except Exception as e:
            self.logger.error(f"处理品种列表更新失败: {e}")
            
    def _on_prices_updated(self, data: List[Dict[str, Any]]) -> None:
        """处理价格数据更新"""
        try:
            self.crypto_data = data
            self._apply_filters_and_sort()
            self.logger.debug(f"更新价格数据: {len(data)}个品种")
            
        except Exception as e:
            self.logger.error(f"处理价格数据更新失败: {e}")
            
    def _on_connection_status(self, connected: bool, message: str) -> None:
        """处理连接状态变化"""
        if connected:
            self.status_label.setText(f"已连接 - {message}")
            self.status_label.setStyleSheet("color: green;")
            self.refresh_btn.setEnabled(True)
        else:
            self.status_label.setText(f"未连接 - {message}")
            self.status_label.setStyleSheet("color: red;")
            self.refresh_btn.setEnabled(False)
            
    def _on_error_occurred(self, error_msg: str) -> None:
        """处理错误"""
        self.logger.error(f"虚拟币数据错误: {error_msg}")
        QMessageBox.warning(self, "错误", f"虚拟币数据错误:\n{error_msg}")
        
    def _on_search_changed(self, text: str) -> None:
        """处理搜索文本变化"""
        self._apply_filters_and_sort()
        
    def _on_sort_changed(self, sort_text: str) -> None:
        """处理排序变化"""
        self._apply_filters_and_sort()
        
    def _on_refresh_clicked(self) -> None:
        """处理刷新按钮点击"""
        if self.view_model:
            self.progress_bar.setVisible(True)
            self.refresh_btn.setEnabled(False)
            self.view_model.refresh_data()
            
            # 2秒后隐藏进度条
            QTimer.singleShot(2000, lambda: (
                self.progress_bar.setVisible(False),
                self.refresh_btn.setEnabled(True)
            ))
            
    def _on_cell_clicked(self, row: int, column: int) -> None:
        """处理单元格点击"""
        try:
            if 0 <= row < len(self.filtered_data):
                symbol = self.filtered_data[row]['symbol']
                self.logger.info(f"选中品种: {symbol}")
                
        except Exception as e:
            self.logger.error(f"处理单元格点击失败: {e}")
            
    def _on_cell_double_clicked(self, row: int, column: int) -> None:
        """处理单元格双击"""
        try:
            if 0 <= row < len(self.filtered_data):
                symbol = self.filtered_data[row]['symbol']
                self.symbol_selected.emit(symbol)
                self.logger.info(f"双击选择品种: {symbol}")
                
        except Exception as e:
            self.logger.error(f"处理单元格双击失败: {e}")
            
    def _apply_filters_and_sort(self) -> None:
        """应用过滤和排序"""
        try:
            # 应用搜索过滤
            search_text = self.search_edit.text().upper() if self.search_edit else ""
            if search_text:
                self.filtered_data = [
                    item for item in self.crypto_data
                    if search_text in item['symbol'].upper() or 
                       search_text in item['baseAsset'].upper()
                ]
            else:
                self.filtered_data = self.crypto_data.copy()
            
            # 应用排序
            sort_text = self.sort_combo.currentText() if self.sort_combo else ""
            if sort_text == "成交量(降序)":
                self.filtered_data.sort(key=lambda x: float(x['volume']), reverse=True)
            elif sort_text == "涨跌幅(降序)":
                self.filtered_data.sort(key=lambda x: float(x['change_percent']), reverse=True)
            elif sort_text == "涨跌幅(升序)":
                self.filtered_data.sort(key=lambda x: float(x['change_percent']))
            elif sort_text == "价格(降序)":
                self.filtered_data.sort(key=lambda x: float(x['price']), reverse=True)
            elif sort_text == "价格(升序)":
                self.filtered_data.sort(key=lambda x: float(x['price']))
            elif sort_text == "品种名称":
                self.filtered_data.sort(key=lambda x: x['symbol'])
            
            # 更新表格显示
            self._update_table_data()
            
        except Exception as e:
            self.logger.error(f"应用过滤和排序失败: {e}")
            
    def _update_table_data(self) -> None:
        """更新表格数据"""
        try:
            self.table.setRowCount(len(self.filtered_data))
            
            for row, item in enumerate(self.filtered_data):
                for col, (_, key, _) in enumerate(self.columns):
                    value = item.get(key, '')
                    
                    # 格式化显示值
                    if key == 'price':
                        text = f"{float(value):.4f}"
                    elif key == 'change':
                        text = f"{float(value):+.4f}"
                    elif key == 'change_percent':
                        text = f"{float(value):+.2f}%"
                    elif key == 'volume':
                        text = f"{float(value):,.0f}"
                    elif key in ['high', 'low']:
                        text = f"{float(value):.4f}"
                    else:
                        text = str(value)
                    
                    # 创建表格项
                    table_item = QTableWidgetItem(text)
                    
                    # 设置颜色（涨跌幅）
                    if key in ['change', 'change_percent']:
                        if float(value) > 0:
                            table_item.setForeground(QColor('green'))
                        elif float(value) < 0:
                            table_item.setForeground(QColor('red'))
                    
                    # 设置对齐方式
                    if key in ['price', 'change', 'change_percent', 'volume', 'high', 'low']:
                        table_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    
                    self.table.setItem(row, col, table_item)
                    
        except Exception as e:
            self.logger.error(f"更新表格数据失败: {e}")
            
    def _update_display(self) -> None:
        """定时更新显示"""
        # 这里可以添加实时更新逻辑，比如闪烁价格变化等
        pass
        
    def get_selected_symbol(self) -> Optional[str]:
        """获取当前选中的品种"""
        current_row = self.table.currentRow()
        if 0 <= current_row < len(self.filtered_data):
            return self.filtered_data[current_row]['symbol']
        return None
