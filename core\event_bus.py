"""
事件总线实现
使用 Python 3.13 的新特性实现高性能、线程安全的事件系统
"""

from __future__ import annotations
import asyncio
import threading
import weakref
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime
from queue import Queue, Empty
from typing import (
    Callable, Dict, List, Set, Any, Optional, Type, 
    TypeVar, Generic, Protocol, runtime_checkable
)
import logging

from .event_types import BaseEvent, LogEvent


# 事件处理器类型
EventHandler = Callable[[BaseEvent], None]
AsyncEventHandler = Callable[[BaseEvent], Any]  # 可以是协程


@runtime_checkable
class EventSubscriber(Protocol):
    """事件订阅者协议"""
    def handle_event(self, event: BaseEvent) -> None:
        """处理事件"""
        ...


@dataclass
class Subscription:
    """订阅信息"""
    event_type: str
    handler: EventHandler
    subscriber_id: str
    priority: int = 0  # 优先级，数字越大优先级越高
    is_async: bool = False
    weak_ref: bool = True  # 是否使用弱引用


class EventBus:
    """
    事件总线 - 系统的通信中枢
    
    特性：
    1. 线程安全的事件分发
    2. 支持同步和异步事件处理器
    3. 优先级队列
    4. 弱引用避免内存泄漏
    5. 事件过滤和中间件支持
    6. 性能监控和统计
    """
    
    def __init__(self, max_workers: int = 4, queue_size: int = 10000):
        self._subscriptions: Dict[str, List[Subscription]] = defaultdict(list)
        self._event_queue: Queue[BaseEvent] = Queue(maxsize=queue_size)
        self._running = False
        self._dispatcher_thread: Optional[threading.Thread] = None
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 统计信息
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'subscribers_count': 0,
            'queue_size': 0,
            'last_event_time': None
        }
        
        # 事件过滤器和中间件
        self._filters: List[Callable[[BaseEvent], bool]] = []
        self._middleware: List[Callable[[BaseEvent], BaseEvent]] = []
        
        # 弱引用管理
        self._weak_refs: Set[weakref.ref] = set()
    
    def start(self) -> None:
        """启动事件总线"""
        if self._running:
            return
            
        self._running = True
        self._dispatcher_thread = threading.Thread(
            target=self._dispatch_loop,
            name="EventBus-Dispatcher",
            daemon=True
        )
        self._dispatcher_thread.start()
        self._logger.info("事件总线已启动")
    
    def stop(self) -> None:
        """停止事件总线"""
        if not self._running:
            return
            
        self._running = False
        
        # 发送停止信号
        try:
            self._event_queue.put_nowait(None)  # 哨兵值
        except:
            pass
            
        # 等待分发线程结束
        if self._dispatcher_thread and self._dispatcher_thread.is_alive():
            self._dispatcher_thread.join(timeout=5.0)
            
        # 关闭线程池
        self._executor.shutdown(wait=True)
        self._logger.info("事件总线已停止")
    
    def subscribe(
        self,
        event_type: str,
        handler: EventHandler,
        subscriber_id: str = None,
        priority: int = 0,
        weak_ref: bool = True
    ) -> str:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理器
            subscriber_id: 订阅者ID，如果为None则自动生成
            priority: 优先级
            weak_ref: 是否使用弱引用
            
        Returns:
            订阅ID
        """
        if subscriber_id is None:
            subscriber_id = f"subscriber_{id(handler)}"
            
        # 检查是否是异步处理器
        is_async = asyncio.iscoroutinefunction(handler)
        
        subscription = Subscription(
            event_type=event_type,
            handler=handler,
            subscriber_id=subscriber_id,
            priority=priority,
            is_async=is_async,
            weak_ref=weak_ref
        )
        
        with self._lock:
            self._subscriptions[event_type].append(subscription)
            # 按优先级排序（高优先级在前）
            self._subscriptions[event_type].sort(key=lambda s: s.priority, reverse=True)
            self._stats['subscribers_count'] += 1
            
            # 如果使用弱引用，添加到弱引用集合
            if weak_ref:
                weak_handler = weakref.ref(handler, self._cleanup_weak_ref)
                self._weak_refs.add(weak_handler)
        
        self._logger.debug(f"订阅事件: {event_type}, 订阅者: {subscriber_id}")
        return subscriber_id
    
    def unsubscribe(self, event_type: str, subscriber_id: str) -> bool:
        """取消订阅"""
        with self._lock:
            subscriptions = self._subscriptions.get(event_type, [])
            for i, subscription in enumerate(subscriptions):
                if subscription.subscriber_id == subscriber_id:
                    del subscriptions[i]
                    self._stats['subscribers_count'] -= 1
                    self._logger.debug(f"取消订阅: {event_type}, 订阅者: {subscriber_id}")
                    return True
        return False
    
    def publish(self, event: BaseEvent) -> bool:
        """
        发布事件
        
        Args:
            event: 要发布的事件
            
        Returns:
            是否成功加入队列
        """
        if not self._running:
            return False
            
        # 应用过滤器
        for filter_func in self._filters:
            if not filter_func(event):
                return False
                
        # 应用中间件
        for middleware in self._middleware:
            event = middleware(event)
            
        try:
            self._event_queue.put_nowait(event)
            self._stats['events_published'] += 1
            self._stats['queue_size'] = self._event_queue.qsize()
            self._stats['last_event_time'] = datetime.now()
            return True
        except:
            self._logger.warning(f"事件队列已满，丢弃事件: {event}")
            return False
    
    def publish_sync(self, event: BaseEvent) -> None:
        """同步发布事件（直接处理，不经过队列）"""
        if not self._running:
            return
            
        self._process_event(event)
    
    def add_filter(self, filter_func: Callable[[BaseEvent], bool]) -> None:
        """添加事件过滤器"""
        self._filters.append(filter_func)
    
    def add_middleware(self, middleware: Callable[[BaseEvent], BaseEvent]) -> None:
        """添加事件中间件"""
        self._middleware.append(middleware)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self._stats.copy()
            stats['queue_size'] = self._event_queue.qsize()
            return stats
    
    def _dispatch_loop(self) -> None:
        """事件分发循环"""
        self._logger.info("事件分发循环已启动")
        
        while self._running:
            try:
                # 从队列获取事件，超时1秒
                event = self._event_queue.get(timeout=1.0)
                
                # 检查停止信号
                if event is None:
                    break
                    
                self._process_event(event)
                self._event_queue.task_done()
                
            except Empty:
                continue
            except Exception as e:
                self._logger.error(f"事件分发循环异常: {e}")
                
        self._logger.info("事件分发循环已结束")
    
    def _process_event(self, event: BaseEvent) -> None:
        """处理单个事件"""
        event_type = event.get_event_type()
        
        with self._lock:
            subscriptions = self._subscriptions.get(event_type, []).copy()
        
        if not subscriptions:
            return
            
        # 清理失效的弱引用
        self._cleanup_dead_subscriptions(event_type)
        
        for subscription in subscriptions:
            try:
                if subscription.is_async:
                    # 异步处理器
                    self._executor.submit(self._handle_async_event, subscription.handler, event)
                else:
                    # 同步处理器
                    self._executor.submit(self._handle_sync_event, subscription.handler, event)
                    
            except Exception as e:
                self._logger.error(f"处理事件失败: {e}, 事件: {event}")
                self._stats['events_failed'] += 1
        
        self._stats['events_processed'] += 1
    
    def _handle_sync_event(self, handler: EventHandler, event: BaseEvent) -> None:
        """处理同步事件"""
        try:
            handler(event)
        except Exception as e:
            self._logger.error(f"同步事件处理器异常: {e}")
            self._stats['events_failed'] += 1
    
    def _handle_async_event(self, handler: AsyncEventHandler, event: BaseEvent) -> None:
        """处理异步事件"""
        try:
            # 在新的事件循环中运行异步处理器
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(handler(event))
            finally:
                loop.close()
        except Exception as e:
            self._logger.error(f"异步事件处理器异常: {e}")
            self._stats['events_failed'] += 1
    
    def _cleanup_weak_ref(self, weak_ref: weakref.ref) -> None:
        """清理弱引用回调"""
        self._weak_refs.discard(weak_ref)
    
    def _cleanup_dead_subscriptions(self, event_type: str) -> None:
        """清理失效的订阅"""
        with self._lock:
            subscriptions = self._subscriptions.get(event_type, [])
            alive_subscriptions = []
            
            for subscription in subscriptions:
                if subscription.weak_ref:
                    # 检查弱引用是否还有效
                    try:
                        # 尝试调用处理器，如果对象已被回收会抛出异常
                        if callable(subscription.handler):
                            alive_subscriptions.append(subscription)
                    except:
                        self._stats['subscribers_count'] -= 1
                else:
                    alive_subscriptions.append(subscription)
            
            self._subscriptions[event_type] = alive_subscriptions


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


def init_event_bus(**kwargs) -> EventBus:
    """初始化全局事件总线"""
    global _global_event_bus
    _global_event_bus = EventBus(**kwargs)
    return _global_event_bus
