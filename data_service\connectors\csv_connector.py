"""
CSV文件数据连接器
用于从CSV文件读取历史数据
"""

from __future__ import annotations
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal

from .base_connector import BaseConnector
from core.data_types import TickData, BarData, SymbolInfo, Interval


class CSVConnector(BaseConnector):
    """
    CSV文件数据连接器
    
    支持从CSV文件读取历史K线数据
    CSV文件格式要求：
    - 文件名格式：{symbol}_{interval}.csv
    - 列名：datetime, open, high, low, close, volume, turnover
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.data_path = Path(config.get('data_path', './data/csv'))
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # 缓存已加载的数据
        self._data_cache: Dict[str, pd.DataFrame] = {}
        self._symbol_info_cache: Dict[str, SymbolInfo] = {}
    
    def connect(self) -> bool:
        """连接到CSV数据源（检查数据目录）"""
        try:
            if not self.data_path.exists():
                self.logger.error(f"数据目录不存在: {self.data_path}")
                return False
            
            # 扫描可用的数据文件
            csv_files = list(self.data_path.glob("*.csv"))
            self.logger.info(f"发现 {len(csv_files)} 个CSV数据文件")
            
            self._on_connection_status(True, f"成功连接到CSV数据源，发现{len(csv_files)}个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"连接CSV数据源失败: {e}")
            self._on_connection_status(False, str(e))
            return False
    
    def disconnect(self) -> bool:
        """断开连接（清理缓存）"""
        try:
            self._data_cache.clear()
            self._symbol_info_cache.clear()
            self._subscribed_symbols.clear()
            self._on_connection_status(False, "已断开CSV数据源连接")
            return True
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            return False
    
    def subscribe(self, symbol: str) -> bool:
        """订阅品种（加载CSV数据到缓存）"""
        try:
            if symbol in self._subscribed_symbols:
                return True
            
            # 查找该品种的数据文件
            pattern = f"{symbol}_*.csv"
            files = list(self.data_path.glob(pattern))
            
            if not files:
                self.logger.warning(f"未找到品种 {symbol} 的数据文件")
                return False
            
            # 加载数据文件
            for file_path in files:
                self._load_csv_file(file_path)
            
            self._subscribed_symbols.append(symbol)
            self.logger.info(f"成功订阅品种: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"订阅品种 {symbol} 失败: {e}")
            return False
    
    def unsubscribe(self, symbol: str) -> bool:
        """取消订阅品种"""
        try:
            if symbol in self._subscribed_symbols:
                self._subscribed_symbols.remove(symbol)
                
                # 清理相关缓存
                keys_to_remove = [key for key in self._data_cache.keys() if key.startswith(f"{symbol}_")]
                for key in keys_to_remove:
                    del self._data_cache[key]
                
                if symbol in self._symbol_info_cache:
                    del self._symbol_info_cache[symbol]
                
                self.logger.info(f"取消订阅品种: {symbol}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"取消订阅品种 {symbol} 失败: {e}")
            return False
    
    def get_symbol_info(self, symbol: str) -> Optional[SymbolInfo]:
        """获取品种信息"""
        if symbol in self._symbol_info_cache:
            return self._symbol_info_cache[symbol]
        
        # 创建默认的品种信息
        symbol_info: SymbolInfo = {
            'symbol': symbol,
            'name': symbol,
            'exchange': 'CSV',
            'product_type': 'unknown',
            'size': Decimal('1'),
            'price_tick': Decimal('0.01'),
            'min_volume': 1,
            'margin_rate': Decimal('0.1')
        }
        
        self._symbol_info_cache[symbol] = symbol_info
        return symbol_info
    
    def get_historical_bars(
        self, 
        symbol: str, 
        start_time: datetime, 
        end_time: datetime,
        interval: str = "1m"
    ) -> List[BarData]:
        """获取历史K线数据"""
        try:
            cache_key = f"{symbol}_{interval}"
            
            if cache_key not in self._data_cache:
                self.logger.warning(f"未找到缓存数据: {cache_key}")
                return []
            
            df = self._data_cache[cache_key]
            
            # 过滤时间范围
            mask = (df['datetime'] >= start_time) & (df['datetime'] <= end_time)
            filtered_df = df[mask]
            
            # 转换为BarData对象
            bars = []
            for _, row in filtered_df.iterrows():
                bar = BarData(
                    symbol=symbol,
                    datetime=row['datetime'],
                    interval=Interval(interval),
                    open_price=Decimal(str(row['open'])),
                    high_price=Decimal(str(row['high'])),
                    low_price=Decimal(str(row['low'])),
                    close_price=Decimal(str(row['close'])),
                    volume=int(row['volume']),
                    turnover=Decimal(str(row.get('turnover', 0)))
                )
                bars.append(bar)
            
            return bars
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []
    
    def _load_csv_file(self, file_path: Path) -> None:
        """加载CSV文件到缓存"""
        try:
            # 从文件名解析品种和周期
            filename = file_path.stem  # 去掉.csv扩展名
            parts = filename.split('_')
            if len(parts) < 2:
                self.logger.warning(f"文件名格式不正确: {filename}")
                return
            
            symbol = parts[0]
            interval = '_'.join(parts[1:])
            cache_key = f"{symbol}_{interval}"
            
            # 读取CSV文件
            df = pd.read_csv(file_path)
            
            # 验证必需的列
            required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"CSV文件缺少必需列: {missing_columns}")
                return
            
            # 转换datetime列
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            # 确保数据按时间排序
            df = df.sort_values('datetime')
            
            # 添加turnover列（如果不存在）
            if 'turnover' not in df.columns:
                df['turnover'] = df['close'] * df['volume']
            
            self._data_cache[cache_key] = df
            self.logger.info(f"成功加载CSV文件: {file_path}, 数据行数: {len(df)}")
            
        except Exception as e:
            self.logger.error(f"加载CSV文件失败 {file_path}: {e}")
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的品种列表"""
        symbols = set()
        for csv_file in self.data_path.glob("*.csv"):
            filename = csv_file.stem
            parts = filename.split('_')
            if len(parts) >= 2:
                symbols.add(parts[0])
        return list(symbols)
    
    def get_available_intervals(self, symbol: str) -> List[str]:
        """获取指定品种的可用时间周期"""
        intervals = []
        pattern = f"{symbol}_*.csv"
        for csv_file in self.data_path.glob(pattern):
            filename = csv_file.stem
            parts = filename.split('_')
            if len(parts) >= 2:
                interval = '_'.join(parts[1:])
                intervals.append(interval)
        return intervals
