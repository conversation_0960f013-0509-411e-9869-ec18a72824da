"""
通知服务
提供多种通知方式：GUI弹窗、声音、邮件等
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List, Callable
import logging
from datetime import datetime
from enum import Enum
import threading
import queue

from core.event_types import (
    RiskWarningEvent, TradeEvent, OrderEvent, LogEvent
)


class NotificationType(Enum):
    """通知类型"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class NotificationChannel(Enum):
    """通知渠道"""
    GUI = "gui"
    SOUND = "sound"
    EMAIL = "email"
    LOG = "log"
    CONSOLE = "console"


class Notification:
    """通知消息"""
    
    def __init__(
        self,
        title: str,
        message: str,
        notification_type: NotificationType = NotificationType.INFO,
        channels: List[NotificationChannel] = None,
        timestamp: datetime = None
    ):
        self.title = title
        self.message = message
        self.type = notification_type
        self.channels = channels or [NotificationChannel.LOG]
        self.timestamp = timestamp or datetime.now()
        self.id = f"notif_{self.timestamp.strftime('%Y%m%d_%H%M%S_%f')}"


class NotificationService:
    """
    通知服务
    
    职责:
    1. 接收各种事件并转换为通知
    2. 根据配置选择通知渠道
    3. 管理通知历史
    4. 提供通知过滤和优先级
    """
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 通知配置
        self.notification_config = config.get('notification', {})
        self.enabled = self.notification_config.get('enabled', True)
        
        # 通知渠道配置
        self.gui_enabled = self.notification_config.get('gui_enabled', True)
        self.sound_enabled = self.notification_config.get('sound_enabled', True)
        self.email_enabled = self.notification_config.get('email_enabled', False)
        
        # 通知历史
        self.notification_history: List[Notification] = []
        self.max_history_size = self.notification_config.get('max_history_size', 1000)
        
        # 通知队列和处理线程
        self.notification_queue: queue.Queue = queue.Queue()
        self.processing_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 通知处理器
        self.handlers: Dict[NotificationChannel, Callable] = {}
        self._setup_handlers()
        
        # 订阅事件
        self._subscribe_events()
        
        # 启动处理线程
        self._start_processing_thread()
    
    def _setup_handlers(self) -> None:
        """设置通知处理器"""
        self.handlers[NotificationChannel.GUI] = self._handle_gui_notification
        self.handlers[NotificationChannel.SOUND] = self._handle_sound_notification
        self.handlers[NotificationChannel.EMAIL] = self._handle_email_notification
        self.handlers[NotificationChannel.LOG] = self._handle_log_notification
        self.handlers[NotificationChannel.CONSOLE] = self._handle_console_notification
    
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("risk_warning", self._on_risk_warning, "notification_risk")
            self.event_bus.subscribe("trade", self._on_trade_event, "notification_trade")
            self.event_bus.subscribe("order", self._on_order_event, "notification_order")
            self.event_bus.subscribe("log", self._on_log_event, "notification_log")
    
    def _start_processing_thread(self) -> None:
        """启动通知处理线程"""
        if not self.enabled:
            return
            
        self.running = True
        self.processing_thread = threading.Thread(
            target=self._process_notifications,
            name="NotificationProcessor",
            daemon=True
        )
        self.processing_thread.start()
        self.logger.info("通知处理线程已启动")
    
    def _process_notifications(self) -> None:
        """处理通知队列"""
        while self.running:
            try:
                # 从队列获取通知（阻塞等待）
                notification = self.notification_queue.get(timeout=1)
                
                # 处理通知
                self._handle_notification(notification)
                
                # 标记任务完成
                self.notification_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理通知失败: {e}")
    
    def _handle_notification(self, notification: Notification) -> None:
        """处理单个通知"""
        try:
            # 添加到历史记录
            self._add_to_history(notification)
            
            # 根据渠道分发通知
            for channel in notification.channels:
                if channel in self.handlers:
                    try:
                        self.handlers[channel](notification)
                    except Exception as e:
                        self.logger.error(f"通知渠道 {channel.value} 处理失败: {e}")
                        
        except Exception as e:
            self.logger.error(f"处理通知失败: {e}")
    
    def _add_to_history(self, notification: Notification) -> None:
        """添加到历史记录"""
        self.notification_history.append(notification)
        
        # 限制历史记录大小
        if len(self.notification_history) > self.max_history_size:
            self.notification_history = self.notification_history[-self.max_history_size:]
    
    def _handle_gui_notification(self, notification: Notification) -> None:
        """处理GUI通知"""
        if not self.gui_enabled:
            return
            
        try:
            # 检查是否有GUI环境
            try:
                from PySide6.QtWidgets import QMessageBox, QApplication
                
                # 检查是否有活动的QApplication
                app = QApplication.instance()
                if app is None:
                    self.logger.debug("无GUI应用实例，跳过GUI通知")
                    return
                
                # 创建消息框（需要在主线程中执行）
                def show_message():
                    if notification.type == NotificationType.ERROR:
                        QMessageBox.critical(None, notification.title, notification.message)
                    elif notification.type == NotificationType.WARNING:
                        QMessageBox.warning(None, notification.title, notification.message)
                    elif notification.type == NotificationType.SUCCESS:
                        QMessageBox.information(None, notification.title, notification.message)
                    else:
                        QMessageBox.information(None, notification.title, notification.message)
                
                # 使用QTimer在主线程中执行
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, show_message)
                
            except ImportError:
                self.logger.debug("GUI库不可用，跳过GUI通知")
                
        except Exception as e:
            self.logger.error(f"GUI通知失败: {e}")
    
    def _handle_sound_notification(self, notification: Notification) -> None:
        """处理声音通知"""
        if not self.sound_enabled:
            return
            
        try:
            # 简单的系统提示音
            import sys
            if sys.platform == "win32":
                import winsound
                if notification.type == NotificationType.ERROR:
                    winsound.MessageBeep(winsound.MB_ICONHAND)
                elif notification.type == NotificationType.WARNING:
                    winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
                else:
                    winsound.MessageBeep(winsound.MB_ICONASTERISK)
            else:
                # Unix/Linux系统
                print('\a')  # 系统提示音
                
        except Exception as e:
            self.logger.error(f"声音通知失败: {e}")
    
    def _handle_email_notification(self, notification: Notification) -> None:
        """处理邮件通知"""
        if not self.email_enabled:
            return
            
        try:
            # 邮件配置
            email_config = self.notification_config.get('email', {})
            if not email_config:
                return
            
            # 这里可以实现邮件发送逻辑
            # 使用 smtplib 发送邮件
            self.logger.info(f"邮件通知: {notification.title} - {notification.message}")
            
        except Exception as e:
            self.logger.error(f"邮件通知失败: {e}")
    
    def _handle_log_notification(self, notification: Notification) -> None:
        """处理日志通知"""
        try:
            log_message = f"[通知] {notification.title}: {notification.message}"
            
            if notification.type == NotificationType.ERROR:
                self.logger.error(log_message)
            elif notification.type == NotificationType.WARNING:
                self.logger.warning(log_message)
            else:
                self.logger.info(log_message)
                
        except Exception as e:
            self.logger.error(f"日志通知失败: {e}")
    
    def _handle_console_notification(self, notification: Notification) -> None:
        """处理控制台通知"""
        try:
            timestamp = notification.timestamp.strftime("%H:%M:%S")
            type_symbol = {
                NotificationType.ERROR: "❌",
                NotificationType.WARNING: "⚠️",
                NotificationType.SUCCESS: "✅",
                NotificationType.INFO: "ℹ️"
            }.get(notification.type, "ℹ️")
            
            print(f"[{timestamp}] {type_symbol} {notification.title}: {notification.message}")
            
        except Exception as e:
            self.logger.error(f"控制台通知失败: {e}")
    
    def _on_risk_warning(self, event: RiskWarningEvent) -> None:
        """处理风险警告事件"""
        notification = Notification(
            title="风险警告",
            message=event.message,
            notification_type=NotificationType.WARNING,
            channels=[NotificationChannel.GUI, NotificationChannel.SOUND, NotificationChannel.LOG]
        )
        self.send_notification(notification)
    
    def _on_trade_event(self, event: TradeEvent) -> None:
        """处理成交事件"""
        trade = event.data
        notification = Notification(
            title="交易成交",
            message=f"{trade.symbol} {trade.direction.value} {trade.volume}@{trade.price}",
            notification_type=NotificationType.SUCCESS,
            channels=[NotificationChannel.LOG, NotificationChannel.CONSOLE]
        )
        self.send_notification(notification)
    
    def _on_order_event(self, event: OrderEvent) -> None:
        """处理订单事件"""
        order = event.data
        
        # 只通知重要的订单状态
        if order.status.value in ['FILLED', 'CANCELLED', 'REJECTED']:
            notification_type = NotificationType.SUCCESS if order.status.value == 'FILLED' else NotificationType.WARNING
            
            notification = Notification(
                title="订单状态",
                message=f"订单 {order.order_id[-8:]} {order.status.value}",
                notification_type=notification_type,
                channels=[NotificationChannel.LOG]
            )
            self.send_notification(notification)
    
    def _on_log_event(self, event: LogEvent) -> None:
        """处理日志事件"""
        # 只通知ERROR级别的日志
        if event.level.upper() == 'ERROR':
            notification = Notification(
                title="系统错误",
                message=event.message,
                notification_type=NotificationType.ERROR,
                channels=[NotificationChannel.CONSOLE]
            )
            self.send_notification(notification)
    
    def send_notification(self, notification: Notification) -> None:
        """发送通知"""
        if not self.enabled:
            return
            
        try:
            self.notification_queue.put_nowait(notification)
        except queue.Full:
            self.logger.warning("通知队列已满，丢弃通知")
    
    def send_custom_notification(
        self,
        title: str,
        message: str,
        notification_type: NotificationType = NotificationType.INFO,
        channels: List[NotificationChannel] = None
    ) -> None:
        """发送自定义通知"""
        notification = Notification(
            title=title,
            message=message,
            notification_type=notification_type,
            channels=channels or [NotificationChannel.LOG]
        )
        self.send_notification(notification)
    
    def get_notification_history(self, count: int = 50) -> List[Notification]:
        """获取通知历史"""
        return self.notification_history[-count:] if len(self.notification_history) >= count else self.notification_history
    
    def clear_notification_history(self) -> None:
        """清空通知历史"""
        self.notification_history.clear()
        self.logger.info("通知历史已清空")
    
    def stop(self) -> None:
        """停止通知服务"""
        self.running = False
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=3)
        
        self.logger.info("通知服务已停止")
