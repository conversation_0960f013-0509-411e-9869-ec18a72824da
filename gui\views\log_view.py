"""
日志视图
显示系统日志信息
"""

from __future__ import annotations
from typing import Any, Optional
import logging

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPlainTextEdit,
    QComboBox, QPushButton, QLabel, QLineEdit,
    QCheckBox, QFrame
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QTextCursor, QFont, QColor

from gui.viewmodels.log_vm import LogViewModel


class LogView(QWidget):
    """
    日志视图
    
    功能:
    1. 显示系统日志
    2. 日志级别过滤
    3. 日志搜索
    4. 日志导出
    5. 自动滚动
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[LogViewModel] = None
        
        # UI组件
        self.log_text: Optional[QTextEdit] = None
        self.level_combo: Optional[QComboBox] = None
        self.search_edit: Optional[QLineEdit] = None
        self.auto_scroll_cb: Optional[QCheckBox] = None
        
        # 配置
        self.max_display_lines = 1000
        self.auto_scroll = True
        
        # 颜色配置
        self.level_colors = {
            'DEBUG': QColor(128, 128, 128),    # 灰色
            'INFO': QColor(0, 0, 0),           # 黑色
            'WARNING': QColor(255, 165, 0),    # 橙色
            'ERROR': QColor(255, 0, 0)         # 红色
        }
        
        # 初始化UI
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = self._create_control_panel()
        layout.addLayout(control_layout)
        
        # 日志显示区域
        self.log_text = QPlainTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumBlockCount(self.max_display_lines)
        layout.addWidget(self.log_text)
        
    def _create_control_panel(self) -> QHBoxLayout:
        """创建控制面板"""
        layout = QHBoxLayout()
        
        # 日志级别过滤
        layout.addWidget(QLabel("级别:"))
        self.level_combo = QComboBox()
        self.level_combo.addItems(['DEBUG', 'INFO', 'WARNING', 'ERROR'])
        self.level_combo.setCurrentText('INFO')
        self.level_combo.currentTextChanged.connect(self._on_level_changed)
        layout.addWidget(self.level_combo)
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索...")
        self.search_edit.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_edit)
        
        # 自动滚动
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        self.auto_scroll_cb.toggled.connect(self._on_auto_scroll_changed)
        layout.addWidget(self.auto_scroll_cb)
        
        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self._on_clear_clicked)
        layout.addWidget(clear_btn)
        
        # 导出按钮
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self._on_export_clicked)
        layout.addWidget(export_btn)
        
        layout.addStretch()
        return layout
        
    def set_view_model(self, view_model: LogViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接信号
        self.view_model.log_added.connect(self._on_log_added)
        self.view_model.logs_cleared.connect(self._on_logs_cleared)
        self.view_model.log_filter_changed.connect(self._on_filter_changed)
        
        self.logger.info("日志ViewModel已连接")
        
    def _on_log_added(self, timestamp: str, level: str, message: str) -> None:
        """新日志添加"""
        try:
            # 格式化日志消息
            log_line = f"[{timestamp}] {level}: {message}"
            
            # 设置颜色
            color = self.level_colors.get(level, QColor(0, 0, 0))
            
            # 添加到文本框
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.End)
            
            # 设置文本颜色
            format = cursor.charFormat()
            format.setForeground(color)
            cursor.setCharFormat(format)
            
            cursor.insertText(log_line + "\n")
            
            # 自动滚动到底部
            if self.auto_scroll:
                self.log_text.ensureCursorVisible()
                
        except Exception as e:
            # 避免日志处理本身出错
            print(f"添加日志失败: {e}")
            
    def _on_logs_cleared(self) -> None:
        """日志清空"""
        self.log_text.clear()
        
    def _on_filter_changed(self, level: str) -> None:
        """过滤器变化"""
        self.level_combo.setCurrentText(level)
        self._refresh_display()
        
    def _on_level_changed(self, level: str) -> None:
        """日志级别变化"""
        if self.view_model:
            self.view_model.set_filter_level(level)
            
    def _on_search_changed(self, keyword: str) -> None:
        """搜索关键词变化"""
        # 简单的文本高亮搜索
        if not keyword:
            return
            
        # 这里可以实现搜索高亮功能
        # 暂时简化处理
        pass
        
    def _on_auto_scroll_changed(self, enabled: bool) -> None:
        """自动滚动状态变化"""
        self.auto_scroll = enabled
        
    def _on_clear_clicked(self) -> None:
        """清空按钮点击"""
        if self.view_model:
            self.view_model.clear_logs()
            
    def _on_export_clicked(self) -> None:
        """导出按钮点击"""
        try:
            from PySide6.QtWidgets import QFileDialog
            from datetime import datetime
            
            # 选择保存文件
            default_name = f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "导出日志", 
                default_name,
                "文本文件 (*.txt);;所有文件 (*)"
            )
            
            if file_path and self.view_model:
                level_filter = self.level_combo.currentText()
                success = self.view_model.export_logs(file_path, level_filter)
                
                if success:
                    self.logger.info(f"日志已导出到: {file_path}")
                else:
                    self.logger.error("日志导出失败")
                    
        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            
    def _refresh_display(self) -> None:
        """刷新显示"""
        if not self.view_model:
            return
            
        try:
            # 获取过滤后的日志
            level_filter = self.level_combo.currentText()
            logs = self.view_model.get_filtered_logs(level_filter)
            
            # 清空并重新显示
            self.log_text.clear()
            
            for log_entry in logs[-self.max_display_lines:]:  # 只显示最近的日志
                timestamp = log_entry['timestamp']
                level = log_entry['level']
                message = log_entry['message']
                
                self._on_log_added(timestamp, level, message)
                
        except Exception as e:
            self.logger.error(f"刷新日志显示失败: {e}")
            
    def append_log(self, level: str, message: str) -> None:
        """直接添加日志（用于测试）"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self._on_log_added(timestamp, level, message)
        
    def get_log_count(self) -> int:
        """获取日志行数"""
        return self.log_text.document().blockCount()
        
    def scroll_to_bottom(self) -> None:
        """滚动到底部"""
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
