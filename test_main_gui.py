#!/usr/bin/env python3
"""
测试main.py的GUI启动
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    
    def test_main_gui():
        print("🧪 测试main.py GUI启动...")
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 导入并创建主控制器
        from core.main_controller import MainController
        
        print("📊 创建主控制器...")
        controller = MainController()
        
        print("🚀 启动系统...")
        controller.start()
        
        print("✅ 系统启动成功！")
        print("📱 GUI窗口应该已经显示")
        print("⏰ 5秒后自动关闭...")
        
        # 5秒后自动关闭
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)
        
        # 运行事件循环
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(test_main_gui())
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
