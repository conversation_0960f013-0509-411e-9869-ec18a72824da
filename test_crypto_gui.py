#!/usr/bin/env python3
"""
测试虚拟币GUI功能
验证虚拟币列表视图和数据获取功能
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import QTimer

from gui.views.crypto_list_view import CryptoListView
from gui.viewmodels.crypto_list_vm import CryptoListViewModel


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


class TestCryptoWindow(QMainWindow):
    """测试虚拟币窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("虚拟币功能测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 配置
        self.config = {
            'data_service': {
                'connectors': {
                    'binance': {
                        'enabled': True,
                        'api_key': '',
                        'api_secret': '',
                        'testnet': False,
                        'base_url': ''
                    }
                }
            }
        }
        
        # 创建ViewModel
        self.crypto_vm = CryptoListViewModel(None, self.config)
        
        # 创建View
        self.crypto_view = CryptoListView()
        self.crypto_view.set_view_model(self.crypto_vm)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试按钮
        test_btn = QPushButton("刷新数据")
        test_btn.clicked.connect(self.crypto_vm.refresh_data)
        layout.addWidget(test_btn)
        
        # 添加虚拟币视图
        layout.addWidget(self.crypto_view)
        
        # 连接信号
        self.crypto_view.symbol_selected.connect(self.on_symbol_selected)
        
        print("✓ 虚拟币测试窗口初始化完成")
        
    def on_symbol_selected(self, symbol: str):
        """处理品种选择"""
        print(f"✓ 选择了品种: {symbol}")
        
    def closeEvent(self, event):
        """关闭事件"""
        print("🔌 断开虚拟币连接...")
        if hasattr(self, 'crypto_vm'):
            self.crypto_vm.disconnect()
        event.accept()


def main():
    """主函数"""
    setup_logging()
    
    print("🚀 开始测试虚拟币GUI功能")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    try:
        # 创建测试窗口
        window = TestCryptoWindow()
        window.show()
        
        print("✅ 虚拟币GUI测试窗口已显示")
        print("📝 请在窗口中测试以下功能:")
        print("   1. 点击'刷新数据'按钮加载虚拟币列表")
        print("   2. 在搜索框中输入品种名称进行搜索")
        print("   3. 使用排序下拉框改变排序方式")
        print("   4. 双击任意品种行测试品种选择功能")
        print("   5. 观察连接状态和数据更新")
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
