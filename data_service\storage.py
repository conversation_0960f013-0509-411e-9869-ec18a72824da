"""
数据存储层
使用高效的文件格式存储历史数据
"""

from __future__ import annotations
import pandas as pd
import pyarrow as pa
import pyarrow.feather as feather
import pyarrow.parquet as pq
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

from core.data_types import TickData, BarData, Interval
from decimal import Decimal


class DataStorage:
    """
    数据存储管理器
    
    支持多种存储格式：Feather、Parquet
    按日期和品种组织数据文件
    """
    
    def __init__(self, storage_path: str, storage_type: str = "feather"):
        self.storage_path = Path(storage_path)
        self.storage_type = storage_type.lower()
        self.logger = logging.getLogger(__name__)
        
        # 创建存储目录
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.tick_path = self.storage_path / "ticks"
        self.bar_path = self.storage_path / "bars"
        self.tick_path.mkdir(exist_ok=True)
        self.bar_path.mkdir(exist_ok=True)
        
        # 线程池用于异步写入
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="DataStorage")
        self._write_lock = threading.Lock()
        
        # 数据缓存
        self._cache_size = 10000
        self._tick_cache: Dict[str, List[TickData]] = {}
        self._bar_cache: Dict[str, List[BarData]] = {}
        
        # 支持的存储格式
        self._supported_formats = ["feather", "parquet"]
        if self.storage_type not in self._supported_formats:
            raise ValueError(f"不支持的存储格式: {storage_type}")
        
        self.logger.info(f"初始化数据存储: {storage_path}, 格式: {storage_type}")
    
    def save_tick_data(self, ticks: List[TickData], async_write: bool = True) -> bool:
        """
        保存Tick数据
        
        Args:
            ticks: Tick数据列表
            async_write: 是否异步写入
            
        Returns:
            bool: 保存是否成功
        """
        if not ticks:
            return True
        
        try:
            # 按品种和日期分组
            grouped_ticks = self._group_ticks_by_symbol_date(ticks)
            
            if async_write:
                # 异步写入
                for key, tick_list in grouped_ticks.items():
                    self._executor.submit(self._write_tick_data, key, tick_list)
                return True
            else:
                # 同步写入
                for key, tick_list in grouped_ticks.items():
                    self._write_tick_data(key, tick_list)
                return True
                
        except Exception as e:
            self.logger.error(f"保存Tick数据失败: {e}")
            return False
    
    def save_bar_data(self, bars: List[BarData], async_write: bool = True) -> bool:
        """
        保存K线数据
        
        Args:
            bars: K线数据列表
            async_write: 是否异步写入
            
        Returns:
            bool: 保存是否成功
        """
        if not bars:
            return True
        
        try:
            # 按品种、周期和日期分组
            grouped_bars = self._group_bars_by_symbol_interval_date(bars)
            
            if async_write:
                # 异步写入
                for key, bar_list in grouped_bars.items():
                    self._executor.submit(self._write_bar_data, key, bar_list)
                return True
            else:
                # 同步写入
                for key, bar_list in grouped_bars.items():
                    self._write_bar_data(key, bar_list)
                return True
                
        except Exception as e:
            self.logger.error(f"保存K线数据失败: {e}")
            return False
    
    def load_tick_data(
        self, 
        symbol: str, 
        start_date: date, 
        end_date: date
    ) -> List[TickData]:
        """
        加载Tick数据
        
        Args:
            symbol: 品种代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[TickData]: Tick数据列表
        """
        try:
            all_ticks = []
            current_date = start_date
            
            while current_date <= end_date:
                file_path = self._get_tick_file_path(symbol, current_date)
                if file_path.exists():
                    ticks = self._read_tick_data(file_path)
                    all_ticks.extend(ticks)
                
                current_date += pd.Timedelta(days=1)
            
            # 按时间排序
            all_ticks.sort(key=lambda x: x.datetime)
            return all_ticks
            
        except Exception as e:
            self.logger.error(f"加载Tick数据失败: {e}")
            return []
    
    def load_bar_data(
        self, 
        symbol: str, 
        interval: Interval,
        start_date: date, 
        end_date: date
    ) -> List[BarData]:
        """
        加载K线数据
        
        Args:
            symbol: 品种代码
            interval: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[BarData]: K线数据列表
        """
        try:
            all_bars = []
            current_date = start_date
            
            while current_date <= end_date:
                file_path = self._get_bar_file_path(symbol, interval, current_date)
                if file_path.exists():
                    bars = self._read_bar_data(file_path)
                    all_bars.extend(bars)
                
                current_date += pd.Timedelta(days=1)
            
            # 按时间排序
            all_bars.sort(key=lambda x: x.datetime)
            return all_bars
            
        except Exception as e:
            self.logger.error(f"加载K线数据失败: {e}")
            return []
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的品种列表"""
        symbols = set()
        
        # 从Tick数据目录扫描
        for file_path in self.tick_path.glob(f"*.{self.storage_type}"):
            parts = file_path.stem.split('_')
            if len(parts) >= 2:
                symbols.add(parts[0])
        
        # 从K线数据目录扫描
        for file_path in self.bar_path.glob(f"*.{self.storage_type}"):
            parts = file_path.stem.split('_')
            if len(parts) >= 3:
                symbols.add(parts[0])
        
        return list(symbols)
    
    def get_available_dates(self, symbol: str) -> List[date]:
        """获取指定品种的可用日期列表"""
        dates = set()
        
        # 从文件名解析日期
        pattern = f"{symbol}_*.{self.storage_type}"
        
        for file_path in self.tick_path.glob(pattern):
            date_str = file_path.stem.split('_')[1]
            try:
                file_date = datetime.strptime(date_str, '%Y%m%d').date()
                dates.add(file_date)
            except ValueError:
                continue
        
        for file_path in self.bar_path.glob(pattern):
            parts = file_path.stem.split('_')
            if len(parts) >= 3:
                date_str = parts[2]
                try:
                    file_date = datetime.strptime(date_str, '%Y%m%d').date()
                    dates.add(file_date)
                except ValueError:
                    continue
        
        return sorted(list(dates))
    
    def _group_ticks_by_symbol_date(self, ticks: List[TickData]) -> Dict[str, List[TickData]]:
        """按品种和日期分组Tick数据"""
        grouped = {}
        for tick in ticks:
            date_str = tick.datetime.strftime('%Y%m%d')
            key = f"{tick.symbol}_{date_str}"
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(tick)
        return grouped
    
    def _group_bars_by_symbol_interval_date(self, bars: List[BarData]) -> Dict[str, List[BarData]]:
        """按品种、周期和日期分组K线数据"""
        grouped = {}
        for bar in bars:
            date_str = bar.datetime.strftime('%Y%m%d')
            key = f"{bar.symbol}_{bar.interval.value}_{date_str}"
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(bar)
        return grouped
    
    def _get_tick_file_path(self, symbol: str, date: date) -> Path:
        """获取Tick数据文件路径"""
        date_str = date.strftime('%Y%m%d')
        filename = f"{symbol}_{date_str}.{self.storage_type}"
        return self.tick_path / filename
    
    def _get_bar_file_path(self, symbol: str, interval: Interval, date: date) -> Path:
        """获取K线数据文件路径"""
        date_str = date.strftime('%Y%m%d')
        filename = f"{symbol}_{interval.value}_{date_str}.{self.storage_type}"
        return self.bar_path / filename
    
    def _write_tick_data(self, key: str, ticks: List[TickData]) -> None:
        """写入Tick数据到文件"""
        try:
            with self._write_lock:
                # 转换为DataFrame
                data = []
                for tick in ticks:
                    data.append({
                        'datetime': tick.datetime,
                        'last_price': float(tick.last_price),
                        'volume': tick.volume,
                        'turnover': float(tick.turnover),
                        'open_interest': tick.open_interest,
                        'bid_price_1': float(tick.bid_price_1),
                        'bid_volume_1': tick.bid_volume_1,
                        'ask_price_1': float(tick.ask_price_1),
                        'ask_volume_1': tick.ask_volume_1
                    })
                
                df = pd.DataFrame(data)
                
                # 获取文件路径
                parts = key.split('_')
                symbol = parts[0]
                date_str = parts[1]
                file_date = datetime.strptime(date_str, '%Y%m%d').date()
                file_path = self._get_tick_file_path(symbol, file_date)
                
                # 写入文件
                if self.storage_type == "feather":
                    feather.write_feather(df, file_path)
                elif self.storage_type == "parquet":
                    df.to_parquet(file_path, index=False)
                
                self.logger.debug(f"写入Tick数据: {file_path}, 记录数: {len(ticks)}")
                
        except Exception as e:
            self.logger.error(f"写入Tick数据失败 {key}: {e}")
    
    def _write_bar_data(self, key: str, bars: List[BarData]) -> None:
        """写入K线数据到文件"""
        try:
            with self._write_lock:
                # 转换为DataFrame
                data = []
                for bar in bars:
                    data.append({
                        'datetime': bar.datetime,
                        'open': float(bar.open_price),
                        'high': float(bar.high_price),
                        'low': float(bar.low_price),
                        'close': float(bar.close_price),
                        'volume': bar.volume,
                        'turnover': float(bar.turnover),
                        'open_interest': bar.open_interest
                    })
                
                df = pd.DataFrame(data)
                
                # 获取文件路径
                parts = key.split('_')
                symbol = parts[0]
                interval_str = parts[1]
                date_str = parts[2]
                interval = Interval(interval_str)
                file_date = datetime.strptime(date_str, '%Y%m%d').date()
                file_path = self._get_bar_file_path(symbol, interval, file_date)
                
                # 写入文件
                if self.storage_type == "feather":
                    feather.write_feather(df, file_path)
                elif self.storage_type == "parquet":
                    df.to_parquet(file_path, index=False)
                
                self.logger.debug(f"写入K线数据: {file_path}, 记录数: {len(bars)}")
                
        except Exception as e:
            self.logger.error(f"写入K线数据失败 {key}: {e}")
    
    def _read_tick_data(self, file_path: Path) -> List[TickData]:
        """从文件读取Tick数据"""
        try:
            if self.storage_type == "feather":
                df = feather.read_feather(file_path)
            elif self.storage_type == "parquet":
                df = pd.read_parquet(file_path)
            else:
                return []
            
            ticks = []
            for _, row in df.iterrows():
                tick = TickData(
                    symbol=file_path.stem.split('_')[0],
                    datetime=row['datetime'],
                    last_price=Decimal(str(row['last_price'])),
                    volume=int(row['volume']),
                    turnover=Decimal(str(row['turnover'])),
                    open_interest=int(row.get('open_interest', 0)),
                    bid_price_1=Decimal(str(row.get('bid_price_1', 0))),
                    bid_volume_1=int(row.get('bid_volume_1', 0)),
                    ask_price_1=Decimal(str(row.get('ask_price_1', 0))),
                    ask_volume_1=int(row.get('ask_volume_1', 0))
                )
                ticks.append(tick)
            
            return ticks
            
        except Exception as e:
            self.logger.error(f"读取Tick数据失败 {file_path}: {e}")
            return []
    
    def _read_bar_data(self, file_path: Path) -> List[BarData]:
        """从文件读取K线数据"""
        try:
            if self.storage_type == "feather":
                df = feather.read_feather(file_path)
            elif self.storage_type == "parquet":
                df = pd.read_parquet(file_path)
            else:
                return []
            
            bars = []
            parts = file_path.stem.split('_')
            symbol = parts[0]
            interval = Interval(parts[1])
            
            for _, row in df.iterrows():
                bar = BarData(
                    symbol=symbol,
                    datetime=row['datetime'],
                    interval=interval,
                    open_price=Decimal(str(row['open'])),
                    high_price=Decimal(str(row['high'])),
                    low_price=Decimal(str(row['low'])),
                    close_price=Decimal(str(row['close'])),
                    volume=int(row['volume']),
                    turnover=Decimal(str(row['turnover'])),
                    open_interest=int(row.get('open_interest', 0))
                )
                bars.append(bar)
            
            return bars
            
        except Exception as e:
            self.logger.error(f"读取K线数据失败 {file_path}: {e}")
            return []
    
    def close(self) -> None:
        """关闭存储管理器"""
        self._executor.shutdown(wait=True)
        self.logger.info("数据存储管理器已关闭")
