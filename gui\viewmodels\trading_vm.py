"""
交易 ViewModel
负责交易操作的业务逻辑
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
from decimal import Decimal
from datetime import datetime

from PySide6.QtCore import QObject, Signal

from core.event_types import SignalEvent
from core.data_types import Direction, OrderType, SignalData


class TradingViewModel(QObject):
    """
    交易ViewModel
    
    职责:
    1. 处理用户交易操作
    2. 生成交易信号
    3. 验证交易参数
    4. 提供交易状态反馈
    """
    
    # 信号定义
    order_submitted = Signal(str)  # order_id
    order_error = Signal(str)  # error_message
    trading_status_changed = Signal(bool)  # is_trading_enabled
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        super().__init__()
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 交易状态
        self.trading_enabled = True
        self.auto_trading = False
        
        # 默认交易参数
        self.default_volume = 100
        self.default_order_type = OrderType.LIMIT
        
    def buy_market(self, symbol: str, volume: int) -> bool:
        """市价买入"""
        return self._submit_order(
            symbol=symbol,
            direction=Direction.LONG,
            order_type=OrderType.MARKET,
            volume=volume,
            price=Decimal('0')
        )
    
    def buy_limit(self, symbol: str, volume: int, price: Decimal) -> bool:
        """限价买入"""
        return self._submit_order(
            symbol=symbol,
            direction=Direction.LONG,
            order_type=OrderType.LIMIT,
            volume=volume,
            price=price
        )
    
    def sell_market(self, symbol: str, volume: int) -> bool:
        """市价卖出"""
        return self._submit_order(
            symbol=symbol,
            direction=Direction.SHORT,
            order_type=OrderType.MARKET,
            volume=volume,
            price=Decimal('0')
        )
    
    def sell_limit(self, symbol: str, volume: int, price: Decimal) -> bool:
        """限价卖出"""
        return self._submit_order(
            symbol=symbol,
            direction=Direction.SHORT,
            order_type=OrderType.LIMIT,
            volume=volume,
            price=price
        )
    
    def _submit_order(
        self, 
        symbol: str, 
        direction: Direction, 
        order_type: OrderType,
        volume: int, 
        price: Decimal
    ) -> bool:
        """
        提交订单
        
        Args:
            symbol: 品种代码
            direction: 交易方向
            order_type: 订单类型
            volume: 数量
            price: 价格
            
        Returns:
            bool: 是否提交成功
        """
        try:
            # 检查交易状态
            if not self.trading_enabled:
                self.order_error.emit("交易已禁用")
                return False
            
            # 验证参数
            if not self._validate_order_params(symbol, volume, price, order_type):
                return False
            
            # 生成订单ID
            order_id = self._generate_order_id()
            
            # 创建交易信号
            signal_data = SignalData(
                symbol=symbol,
                direction=direction,
                order_type=order_type,
                volume=volume,
                price=price,
                datetime=datetime.now(),
                signal_id=order_id,
                strategy_name="manual_trading",
                confidence=1.0
            )
            
            # 发布交易信号事件
            signal_event = SignalEvent(signal_data)
            
            if self.event_bus:
                success = self.event_bus.publish(signal_event)
                if success:
                    self.order_submitted.emit(order_id)
                    self.logger.info(f"提交订单: {symbol} {direction.value} {volume}@{price}")
                    return True
                else:
                    self.order_error.emit("事件发布失败")
                    return False
            else:
                self.order_error.emit("事件总线未初始化")
                return False
                
        except Exception as e:
            error_msg = f"提交订单失败: {e}"
            self.logger.error(error_msg)
            self.order_error.emit(error_msg)
            return False
    
    def _validate_order_params(
        self, 
        symbol: str, 
        volume: int, 
        price: Decimal, 
        order_type: OrderType
    ) -> bool:
        """验证订单参数"""
        try:
            # 检查品种代码
            if not symbol or not symbol.strip():
                self.order_error.emit("品种代码不能为空")
                return False
            
            # 检查数量
            if volume <= 0:
                self.order_error.emit("订单数量必须大于0")
                return False
            
            # 检查价格（限价单）
            if order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                if price <= 0:
                    self.order_error.emit("限价单价格必须大于0")
                    return False
            
            # 检查最大数量限制
            max_volume = self.config.get('trading', {}).get('max_order_volume', 10000)
            if volume > max_volume:
                self.order_error.emit(f"订单数量不能超过{max_volume}")
                return False
            
            return True
            
        except Exception as e:
            self.order_error.emit(f"参数验证失败: {e}")
            return False
    
    def _generate_order_id(self) -> str:
        """生成订单ID"""
        from uuid import uuid4
        return f"manual_{uuid4().hex[:8]}"
    
    def set_trading_enabled(self, enabled: bool) -> None:
        """设置交易状态"""
        self.trading_enabled = enabled
        self.trading_status_changed.emit(enabled)
        self.logger.info(f"交易状态: {'启用' if enabled else '禁用'}")
    
    def set_auto_trading(self, enabled: bool) -> None:
        """设置自动交易"""
        self.auto_trading = enabled
        self.logger.info(f"自动交易: {'启用' if enabled else '禁用'}")
    
    def set_default_volume(self, volume: int) -> None:
        """设置默认数量"""
        if volume > 0:
            self.default_volume = volume
            self.logger.info(f"默认数量设置为: {volume}")
    
    def get_trading_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        return {
            'trading_enabled': self.trading_enabled,
            'auto_trading': self.auto_trading,
            'default_volume': self.default_volume,
            'default_order_type': self.default_order_type.value
        }
    
    def close_position(self, symbol: str, direction: Direction, volume: int) -> bool:
        """平仓"""
        try:
            # 平仓方向与持仓方向相反
            close_direction = Direction.SHORT if direction == Direction.LONG else Direction.LONG
            
            return self._submit_order(
                symbol=symbol,
                direction=close_direction,
                order_type=OrderType.MARKET,
                volume=volume,
                price=Decimal('0')
            )
            
        except Exception as e:
            error_msg = f"平仓失败: {e}"
            self.logger.error(error_msg)
            self.order_error.emit(error_msg)
            return False
    
    def close_all_positions(self, symbol: str) -> bool:
        """平掉指定品种的所有持仓"""
        try:
            # 这里需要从投资组合ViewModel获取持仓信息
            # 简化实现，实际应该注入投资组合ViewModel
            self.logger.info(f"请求平掉所有{symbol}持仓")
            return True
            
        except Exception as e:
            error_msg = f"平仓失败: {e}"
            self.logger.error(error_msg)
            self.order_error.emit(error_msg)
            return False
