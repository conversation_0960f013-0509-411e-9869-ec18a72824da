"""
订单执行器
负责订单的创建、提交、跟踪和管理
"""

from __future__ import annotations
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from decimal import Decimal
import threading
import time
import logging
from uuid import uuid4

from core.data_types import (
    OrderData, TradeData, Direction, OrderType, OrderStatus
)
from core.event_types import (
    BaseEvent, SignalEvent, OrderEvent, TradeEvent, OrderUpdateEvent
)


class Order:
    """订单对象"""
    
    def __init__(self, order_data: OrderData):
        self.data = order_data
        self.create_time = datetime.now()
        self.update_time = datetime.now()
        self.filled_volume = 0
        self.remaining_volume = order_data.volume
        self.avg_price = Decimal('0')
        self.commission = Decimal('0')
        self.trades: List[TradeData] = []
    
    def add_fill(self, trade: TradeData) -> None:
        """添加成交记录"""
        self.trades.append(trade)
        self.filled_volume += trade.volume
        self.remaining_volume = max(0, self.data.volume - self.filled_volume)

        # 更新平均成交价格
        if self.filled_volume > 0:
            total_value = sum(t.price * t.volume for t in self.trades)
            self.avg_price = total_value / self.filled_volume

        # 更新手续费
        self.commission += trade.commission

        # 更新订单状态（创建新的OrderData对象）
        if self.remaining_volume == 0:
            new_status = OrderStatus.FILLED
        elif self.filled_volume > 0:
            new_status = OrderStatus.PARTIAL_FILLED
        else:
            new_status = self.data.status

        # 创建新的OrderData对象
        from core.data_types import OrderData
        self.data = OrderData(
            symbol=self.data.symbol,
            order_id=self.data.order_id,
            direction=self.data.direction,
            order_type=self.data.order_type,
            volume=self.data.volume,
            price=self.data.price,
            status=new_status,
            datetime=self.data.datetime,
            filled_volume=self.filled_volume,
            avg_price=self.avg_price,
            commission=self.commission
        )

        self.update_time = datetime.now()
    
    def cancel(self) -> bool:
        """取消订单"""
        if self.data.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]:
            # 创建新的OrderData对象
            from core.data_types import OrderData
            self.data = OrderData(
                symbol=self.data.symbol,
                order_id=self.data.order_id,
                direction=self.data.direction,
                order_type=self.data.order_type,
                volume=self.data.volume,
                price=self.data.price,
                status=OrderStatus.CANCELLED,
                datetime=self.data.datetime,
                filled_volume=self.filled_volume,
                avg_price=self.avg_price,
                commission=self.commission
            )
            self.update_time = datetime.now()
            return True
        return False
    
    def is_active(self) -> bool:
        """检查订单是否活跃"""
        return self.data.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]
    
    def is_finished(self) -> bool:
        """检查订单是否完成"""
        return self.data.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]


class SimulatedBroker:
    """
    模拟券商接口
    用于模拟交易环境下的订单执行
    """
    
    def __init__(self, commission_rate: Decimal = Decimal('0.0003'), slippage: Decimal = Decimal('0.001')):
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.logger = logging.getLogger(f"{__name__}.SimulatedBroker")
        
        # 市场价格缓存
        self._market_prices: Dict[str, Decimal] = {}
        
        # 订单执行延迟（秒）
        self.execution_delay = 0.1
    
    def set_market_price(self, symbol: str, price: Decimal) -> None:
        """设置市场价格"""
        self._market_prices[symbol] = price
    
    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        try:
            # 检查市场价格
            if order.data.symbol not in self._market_prices:
                self.logger.warning(f"未找到 {order.data.symbol} 的市场价格")
                self._set_order_status(order, OrderStatus.REJECTED)
                return False
            
            market_price = self._market_prices[order.data.symbol]
            
            # 模拟订单执行
            if order.data.order_type == OrderType.MARKET:
                # 市价单立即执行
                execution_price = self._calculate_execution_price(market_price, order.data.direction)
                self._execute_order(order, execution_price, order.data.volume)
                return True
            
            elif order.data.order_type == OrderType.LIMIT:
                # 限价单检查价格条件
                if self._can_execute_limit_order(order.data, market_price):
                    execution_price = order.data.price
                    self._execute_order(order, execution_price, order.data.volume)
                    return True
                else:
                    self._set_order_status(order, OrderStatus.SUBMITTED)
                    return True

            else:
                self.logger.warning(f"不支持的订单类型: {order.data.order_type}")
                self._set_order_status(order, OrderStatus.REJECTED)
                return False

        except Exception as e:
            self.logger.error(f"提交订单失败: {e}")
            self._set_order_status(order, OrderStatus.REJECTED)
            return False
    
    def check_limit_orders(self, orders: List[Order]) -> List[TradeData]:
        """检查限价单是否可以执行"""
        trades = []
        
        for order in orders:
            if (order.data.order_type == OrderType.LIMIT and 
                order.data.status == OrderStatus.SUBMITTED and
                order.data.symbol in self._market_prices):
                
                market_price = self._market_prices[order.data.symbol]
                
                if self._can_execute_limit_order(order.data, market_price):
                    execution_price = order.data.price
                    trade = self._create_trade(order, execution_price, order.remaining_volume)
                    trades.append(trade)
                    order.add_fill(trade)
        
        return trades
    
    def _calculate_execution_price(self, market_price: Decimal, direction: Direction) -> Decimal:
        """计算执行价格（包含滑点）"""
        if direction == Direction.LONG:
            return market_price * (1 + self.slippage)
        else:
            return market_price * (1 - self.slippage)
    
    def _can_execute_limit_order(self, order_data: OrderData, market_price: Decimal) -> bool:
        """检查限价单是否可以执行"""
        if order_data.direction == Direction.LONG:
            # 买入限价单：市价 <= 限价
            return market_price <= order_data.price
        else:
            # 卖出限价单：市价 >= 限价
            return market_price >= order_data.price
    
    def _execute_order(self, order: Order, price: Decimal, volume: int) -> None:
        """执行订单"""
        trade = self._create_trade(order, price, volume)
        order.add_fill(trade)
    
    def _create_trade(self, order: Order, price: Decimal, volume: int) -> TradeData:
        """创建成交记录"""
        commission = price * volume * self.commission_rate
        
        trade = TradeData(
            symbol=order.data.symbol,
            trade_id=f"T_{uuid4().hex[:8]}",
            order_id=order.data.order_id,
            direction=order.data.direction,
            volume=volume,
            price=price,
            datetime=datetime.now(),
            commission=commission
        )
        
        return trade

    def _set_order_status(self, order: Order, status: OrderStatus) -> None:
        """设置订单状态"""
        from core.data_types import OrderData
        order.data = OrderData(
            symbol=order.data.symbol,
            order_id=order.data.order_id,
            direction=order.data.direction,
            order_type=order.data.order_type,
            volume=order.data.volume,
            price=order.data.price,
            status=status,
            datetime=order.data.datetime,
            filled_volume=order.filled_volume,
            avg_price=order.avg_price,
            commission=order.commission
        )


class OrderExecutor:
    """
    订单执行器
    
    特性：
    1. 订单生命周期管理
    2. 多种订单类型支持
    3. 实时订单跟踪
    4. 风险检查
    5. 执行统计
    """
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 订单管理
        self.orders: Dict[str, Order] = {}
        self.active_orders: Dict[str, Order] = {}
        
        # 券商接口
        self.broker: Optional[SimulatedBroker] = None
        
        # 运行状态
        self._running = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        
        # 配置
        execution_config = config.get('execution', {})
        self.simulation_mode = execution_config.get('simulation_mode', True)
        commission_rate = Decimal(str(execution_config.get('commission_rate', 0.0003)))
        
        # 初始化券商接口
        if self.simulation_mode:
            self.broker = SimulatedBroker(commission_rate=commission_rate)
        
        # 统计信息
        self.stats = {
            'total_orders': 0,
            'filled_orders': 0,
            'cancelled_orders': 0,
            'rejected_orders': 0,
            'total_volume': 0,
            'total_commission': Decimal('0')
        }
    
    def start(self) -> None:
        """启动订单执行器"""
        if self._running:
            self.logger.warning("订单执行器已在运行")
            return
        
        try:
            self.logger.info("启动订单执行器...")
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动监控线程
            self._start_monitor()
            
            self._running = True
            self.logger.info("订单执行器启动成功")
            
        except Exception as e:
            self.logger.error(f"启动订单执行器失败: {e}")
            raise
    
    def stop(self) -> None:
        """停止订单执行器"""
        if not self._running:
            return
        
        try:
            self.logger.info("停止订单执行器...")
            self._running = False
            
            # 取消所有活跃订单
            self._cancel_all_orders()
            
            # 停止监控线程
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            
            self.logger.info("订单执行器已停止")
            
        except Exception as e:
            self.logger.error(f"停止订单执行器失败: {e}")
    
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            # 订阅交易信号
            self.event_bus.subscribe("signal", self._on_signal_event, "order_executor_signal")
            
            # 订阅价格更新
            self.event_bus.subscribe("tick", self._on_price_update, "order_executor_price")
            self.event_bus.subscribe("bar", self._on_price_update, "order_executor_price")
    
    def _on_signal_event(self, event: SignalEvent) -> None:
        """处理交易信号"""
        try:
            with self._lock:
                order_id = self._create_order_from_signal(event)
                if order_id:
                    self.logger.info(f"根据信号创建订单: {order_id}")
                    
        except Exception as e:
            self.logger.error(f"处理交易信号失败: {e}")
    
    def _on_price_update(self, event) -> None:
        """处理价格更新"""
        try:
            with self._lock:
                if hasattr(event, 'data'):
                    if hasattr(event.data, 'symbol') and hasattr(event.data, 'last_price'):
                        # Tick数据
                        symbol = event.data.symbol
                        price = event.data.last_price
                    elif hasattr(event.data, 'symbol') and hasattr(event.data, 'close_price'):
                        # K线数据
                        symbol = event.data.symbol
                        price = event.data.close_price
                    else:
                        return
                    
                    # 更新券商的市场价格
                    if self.broker:
                        self.broker.set_market_price(symbol, price)
                    
        except Exception as e:
            self.logger.error(f"处理价格更新失败: {e}")
    
    def _create_order_from_signal(self, signal: SignalEvent) -> Optional[str]:
        """根据信号创建订单"""
        try:
            order_id = f"ORD_{uuid4().hex[:8]}"
            
            # 确定订单类型和价格
            if signal.price is None:
                order_type = OrderType.MARKET
                price = Decimal('0')  # 市价单价格为0
            else:
                order_type = OrderType.LIMIT
                price = Decimal(str(signal.price))
            
            # 创建订单数据
            order_data = OrderData(
                symbol=signal.symbol,
                order_id=order_id,
                direction=Direction.LONG if signal.direction == "LONG" else Direction.SHORT,
                order_type=order_type,
                volume=signal.volume,
                price=price,
                status=OrderStatus.PENDING,
                datetime=datetime.now()
            )
            
            # 创建订单对象
            order = Order(order_data)
            
            # 提交订单
            if self._submit_order(order):
                return order_id
            
            return None
            
        except Exception as e:
            self.logger.error(f"创建订单失败: {e}")
            return None
    
    def _submit_order(self, order: Order) -> bool:
        """提交订单"""
        try:
            # 添加到订单管理
            self.orders[order.data.order_id] = order
            
            # 提交给券商
            if self.broker and self.broker.submit_order(order):
                if order.is_active():
                    self.active_orders[order.data.order_id] = order
                
                # 更新统计
                self.stats['total_orders'] += 1
                
                # 发布订单事件
                self._publish_order_event(order)
                
                # 如果订单已成交，处理成交
                if order.trades:
                    for trade in order.trades:
                        self._publish_trade_event(trade)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"提交订单失败: {e}")
            return False
    
    def _start_monitor(self) -> None:
        """启动监控线程"""
        def monitor_worker():
            """监控工作线程"""
            while self._running:
                try:
                    with self._lock:
                        # 检查限价单
                        if self.broker:
                            active_orders = list(self.active_orders.values())
                            trades = self.broker.check_limit_orders(active_orders)
                            
                            # 处理新成交
                            for trade in trades:
                                self._publish_trade_event(trade)
                            
                            # 清理已完成的订单
                            self._cleanup_finished_orders()
                    
                    time.sleep(0.1)  # 100ms检查一次
                    
                except Exception as e:
                    self.logger.error(f"监控线程异常: {e}")
                    time.sleep(1)
        
        self._monitor_thread = threading.Thread(
            target=monitor_worker,
            name="OrderExecutor-Monitor",
            daemon=True
        )
        self._monitor_thread.start()
    
    def _cleanup_finished_orders(self) -> None:
        """清理已完成的订单"""
        finished_order_ids = []
        
        for order_id, order in self.active_orders.items():
            if order.is_finished():
                finished_order_ids.append(order_id)
                
                # 更新统计
                if order.data.status == OrderStatus.FILLED:
                    self.stats['filled_orders'] += 1
                elif order.data.status == OrderStatus.CANCELLED:
                    self.stats['cancelled_orders'] += 1
                elif order.data.status == OrderStatus.REJECTED:
                    self.stats['rejected_orders'] += 1
                
                self.stats['total_volume'] += order.filled_volume
                self.stats['total_commission'] += order.commission
        
        # 从活跃订单中移除
        for order_id in finished_order_ids:
            del self.active_orders[order_id]
    
    def _cancel_all_orders(self) -> None:
        """取消所有活跃订单"""
        with self._lock:
            for order in list(self.active_orders.values()):
                if order.cancel():
                    self.logger.info(f"取消订单: {order.data.order_id}")
                    self._publish_order_event(order)
    
    def _publish_order_event(self, order: Order) -> None:
        """发布订单事件"""
        if self.event_bus:
            order_event = OrderEvent(
                data=order.data,
                source="order_executor"
            )
            self.event_bus.publish(order_event)
    
    def _publish_trade_event(self, trade: TradeData) -> None:
        """发布成交事件"""
        if self.event_bus:
            trade_event = TradeEvent(
                data=trade,
                source="order_executor"
            )
            self.event_bus.publish(trade_event)
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        with self._lock:
            order = self.active_orders.get(order_id)
            if order and order.cancel():
                self.logger.info(f"取消订单: {order_id}")
                self._publish_order_event(order)
                return True
            return False
    
    def get_order(self, order_id: str) -> Optional[OrderData]:
        """获取订单信息"""
        order = self.orders.get(order_id)
        if order:
            return order.data
        return None
    
    def get_active_orders(self) -> List[OrderData]:
        """获取活跃订单列表"""
        with self._lock:
            return [order.data for order in self.active_orders.values()]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['active_orders'] = len(self.active_orders)
        stats['total_commission'] = float(stats['total_commission'])
        return stats
