#!/usr/bin/env python3
"""
系统启动测试
验证基础架构是否正常工作
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.event_bus import EventBus
from core.event_types import LogEvent, SystemStatusEvent
from infrastructure.config_manager import ConfigManager
from infrastructure.logger import setup_logger


def test_event_bus():
    """测试事件总线"""
    print("测试事件总线...")
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 测试事件处理
    received_events = []
    
    def event_handler(event):
        received_events.append(event)
        print(f"收到事件: {event.EVENT_TYPE}")
    
    # 订阅事件
    event_bus.subscribe("log", event_handler)
    event_bus.subscribe("system_status", event_handler)
    
    # 发布测试事件
    log_event = LogEvent(level="INFO", message="测试日志事件", module="test")
    status_event = SystemStatusEvent(status="testing", message="系统测试中")
    
    event_bus.publish(log_event)
    event_bus.publish(status_event)
    
    # 等待事件处理
    time.sleep(1)
    
    # 停止事件总线
    event_bus.stop()
    
    # 验证结果
    assert len(received_events) == 2
    print("✓ 事件总线测试通过")


def test_config_manager():
    """测试配置管理器"""
    print("测试配置管理器...")
    
    config = ConfigManager()
    
    # 测试获取配置
    system_name = config.get('system.name', 'Unknown')
    log_level = config.get('logging.level', 'INFO')
    
    print(f"系统名称: {system_name}")
    print(f"日志级别: {log_level}")
    
    # 测试设置配置
    config.set('test.value', 'test_data')
    assert config.get('test.value') == 'test_data'
    
    print("✓ 配置管理器测试通过")


def test_logger():
    """测试日志系统"""
    print("测试日志系统...")
    
    logger = setup_logger(
        name="test_logger",
        level="DEBUG",
        enable_console=True,
        enable_events=False  # 避免循环依赖
    )
    
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
    
    print("✓ 日志系统测试通过")


def test_data_types():
    """测试数据类型"""
    print("测试数据类型...")
    
    from core.data_types import TickData, BarData, Direction, Interval
    from decimal import Decimal
    from datetime import datetime
    
    # 测试 TickData
    tick = TickData(
        symbol="AAPL",
        datetime=datetime.now(),
        last_price=Decimal('150.50'),
        volume=1000,
        turnover=Decimal('150500.00')
    )
    
    assert tick.symbol == "AAPL"
    assert tick.last_price == Decimal('150.50')
    
    # 测试 BarData
    bar = BarData(
        symbol="AAPL",
        datetime=datetime.now(),
        interval=Interval.MINUTE_1,
        open_price=Decimal('150.00'),
        high_price=Decimal('151.00'),
        low_price=Decimal('149.50'),
        close_price=Decimal('150.50'),
        volume=10000,
        turnover=Decimal('1505000.00')
    )
    
    assert bar.symbol == "AAPL"
    assert bar.interval == Interval.MINUTE_1
    
    print("✓ 数据类型测试通过")


def test_main_controller():
    """测试主控制器（简化版）"""
    print("测试主控制器...")
    
    from core.main_controller import MainController
    
    # 创建主控制器
    controller = MainController()
    
    # 测试配置和事件总线初始化
    assert controller._config is not None
    assert controller._logger is not None
    
    print("✓ 主控制器测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行基础架构测试")
    print("=" * 50)
    
    try:
        test_config_manager()
        test_logger()
        test_data_types()
        test_event_bus()
        test_main_controller()
        
        print("=" * 50)
        print("✓ 所有测试通过！基础架构工作正常")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
