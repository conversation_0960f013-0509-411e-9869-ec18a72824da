#!/usr/bin/env python3
"""
个人量化交易系统主入口
使用 Python 3.13 新特性构建的事件驱动量化交易平台
"""

import sys
import signal
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# 检查GUI库可用性
try:
    from PySide6.QtWidgets import QApplication
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("PySide6未安装，将使用控制台模式")

from core.main_controller import MainController
from infrastructure.logger import setup_logger


def signal_handler(signum, frame):
    """优雅关闭信号处理器"""
    print(f"\n收到信号 {signum}，正在优雅关闭系统...")
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 初始化日志系统
    logger = setup_logger()
    logger.info("启动量化交易系统...")

    # 创建QApplication（如果GUI可用）
    app = None
    if GUI_AVAILABLE:
        app = QApplication(sys.argv)
        app.setApplicationName("Gemini Quant")
        app.setApplicationVersion("1.0")
        logger.info("QApplication创建成功")

    try:
        # 创建主控制器
        controller = MainController()

        # 启动控制器（但不进入主循环）
        controller.start()

        # 如果有GUI，启动GUI事件循环
        if app:
            logger.info("启动GUI事件循环...")
            logger.info("GUI窗口将保持显示，直到手动关闭")

            # 获取GUI模块并显示窗口
            if 'gui' in controller._modules and controller._modules['gui']:
                gui_window = controller._modules['gui']
                if hasattr(gui_window, 'show'):
                    gui_window.show()
                    logger.info("GUI窗口已显示")

            # 启动Qt事件循环 - 这会保持程序运行直到窗口关闭
            return app.exec()
        else:
            logger.info("控制台模式运行...")
            # 控制台模式，等待中断信号
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("收到中断信号")

    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        if 'controller' in locals():
            controller.stop()


if __name__ == "__main__":
    main()
