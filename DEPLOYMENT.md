# Gemini Quant 部署指南

## 环境要求

### Python 版本
- **Python 3.13+** （推荐使用最新版本）
- 支持 Free-threaded 模式（可选）

### 操作系统
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 20.04+, CentOS 8+)

### 硬件要求
- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **存储**: 10GB可用空间（推荐SSD）
- **网络**: 稳定的互联网连接

## 安装步骤

### 1. 安装Python 3.13

#### Windows
```bash
# 使用官方安装包
# 下载地址: https://www.python.org/downloads/
# 或使用包管理器
winget install Python.Python.3.13
```

#### macOS
```bash
# 使用Homebrew
brew install python@3.13

# 或使用pyenv
pyenv install 3.13.0
pyenv global 3.13.0
```

#### Linux (Ubuntu)
```bash
# 添加deadsnakes PPA
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

# 安装Python 3.13
sudo apt install python3.13 python3.13-venv python3.13-dev
```

### 2. 克隆项目
```bash
git clone https://github.com/your-username/gemini-quant.git
cd gemini-quant
```

### 3. 创建虚拟环境
```bash
# 创建虚拟环境
python3.13 -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 4. 安装依赖包

#### 基础依赖
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### 可选依赖

**技术分析库 (TA-Lib)**
```bash
# Windows
# 下载对应版本的whl文件
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.25-cp313-cp313-win_amd64.whl

# macOS
brew install ta-lib
pip install TA-Lib

# Linux
sudo apt-get install libta-lib-dev
pip install TA-Lib
```

**加密货币数据源**
```bash
pip install ccxt
```

**股票数据源**
```bash
pip install yfinance akshare
```

**数据库支持**
```bash
pip install sqlalchemy redis
```

### 5. 配置系统

#### 创建配置目录
```bash
mkdir -p config data logs
```

#### 编辑配置文件
系统首次运行时会自动创建 `config/default_config.yaml`，你可以创建 `config/user_config.yaml` 来覆盖默认配置：

```yaml
# config/user_config.yaml
system:
  debug: true
  log_level: DEBUG

data_service:
  storage_path: "./data"
  connectors:
    csv:
      enabled: true
      data_path: "./data/csv"

execution:
  simulation_mode: true
  initial_capital: 1000000.0

gui:
  enabled: true
  theme: "dark"
```

## 运行系统

### 1. 基础测试
```bash
# 测试基础架构
python test_startup.py

# 测试数据服务
python test_data_service_simple.py

# 测试策略引擎
python test_strategy_engine.py

# 测试交易执行
python test_execution_portfolio.py

# 完整系统测试
python test_full_system.py
```

### 2. 启动系统
```bash
# 启动完整系统
python main.py
```

### 3. 使用控制台界面
系统启动后会显示控制台界面，包含：
- 账户信息
- 持仓状态
- 最新价格
- 订单记录
- 成交记录
- 系统日志

按 `Ctrl+C` 退出系统。

## 数据准备

### 1. CSV数据格式
将历史数据放在 `data/csv/` 目录下，文件命名格式：`{symbol}_{interval}.csv`

示例：`AAPL_1m.csv`
```csv
datetime,open,high,low,close,volume,turnover
2024-01-02 09:30:00,185.64,186.89,185.32,186.89,1000000,186890000
2024-01-02 09:31:00,186.89,187.12,186.45,186.78,950000,177441000
```

### 2. 实时数据源
配置实时数据连接器（需要相应的API密钥）：

```yaml
data_service:
  connectors:
    binance:
      enabled: true
      api_key: "your_api_key"
      secret_key: "your_secret_key"
```

## 策略开发

### 1. 创建自定义策略
```python
# strategies/my_strategy.py
from strategies.base_strategy import BaseStrategy
from core.data_types import BarData

class MyStrategy(BaseStrategy):
    def on_init(self):
        self.subscribed_symbols = ['AAPL']
    
    def on_start(self):
        self.logger.info("我的策略启动")
    
    def on_stop(self):
        self.logger.info("我的策略停止")
    
    def on_tick(self, tick):
        pass
    
    def on_bar(self, bar):
        # 策略逻辑
        if self.should_buy(bar):
            self.buy(bar.symbol, 100)
```

### 2. 回测策略
```python
from strategy_ai_engine.backtester import BacktestEngine
from strategies.my_strategy import MyStrategy

# 创建回测引擎
engine = BacktestEngine(initial_capital=100000)

# 添加策略
strategy = MyStrategy('my_strategy')
engine.add_strategy(strategy)

# 加载数据
engine.load_data('AAPL', historical_bars)

# 运行回测
result = engine.run_backtest(start_date, end_date)
print(f"总收益率: {result['total_return']:.2%}")
```

## 性能优化

### 1. Python 3.13 Free-threaded 模式
```bash
# 启用无GIL模式（实验性）
export PYTHON_GIL=0
python main.py
```

### 2. 内存优化
```yaml
# config/user_config.yaml
data_service:
  cache_size: 5000  # 减少缓存大小
  
strategy_engine:
  max_strategies: 5  # 限制策略数量
```

### 3. 存储优化
```yaml
data_service:
  storage_type: "parquet"  # 使用Parquet格式
  compression: "snappy"    # 启用压缩
```

## 监控和维护

### 1. 日志管理
日志文件位置：`logs/quant_trader.log`

```bash
# 查看实时日志
tail -f logs/quant_trader.log

# 日志轮转配置
logging:
  max_file_size: "50MB"
  backup_count: 10
```

### 2. 性能监控
```bash
# 内存使用分析
python -m memory_profiler main.py

# 性能分析
python -m cProfile -o profile.stats main.py
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"
```

### 3. 数据备份
```bash
# 备份数据目录
tar -czf backup_$(date +%Y%m%d).tar.gz data/ config/ logs/

# 定期清理日志
find logs/ -name "*.log.*" -mtime +30 -delete
```

## 故障排除

### 1. 常见问题

**ImportError: No module named 'xxx'**
```bash
pip install xxx
```

**TA-Lib安装失败**
- Windows: 下载预编译的whl文件
- macOS: 先安装ta-lib库 `brew install ta-lib`
- Linux: 先安装开发包 `sudo apt-get install libta-lib-dev`

**多进程启动失败**
- 检查Python版本是否支持
- 在Windows上可能需要管理员权限
- 可以禁用多进程模式：`simulation_mode: true`

**内存不足**
- 减少数据缓存大小
- 限制策略数量
- 使用更高效的数据格式

### 2. 调试模式
```yaml
# config/user_config.yaml
system:
  debug: true
  log_level: DEBUG

logging:
  level: DEBUG
```

### 3. 获取帮助
- 查看项目文档：`README.md`
- 检查测试用例：`test_*.py`
- 提交Issue：GitHub Issues
- 邮件联系：<EMAIL>

## 生产环境部署

### 1. 系统服务
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/gemini-quant.service
```

```ini
[Unit]
Description=Gemini Quant Trading System
After=network.target

[Service]
Type=simple
User=trader
WorkingDirectory=/home/<USER>/gemini-quant
Environment=PATH=/home/<USER>/gemini-quant/venv/bin
ExecStart=/home/<USER>/gemini-quant/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable gemini-quant
sudo systemctl start gemini-quant
sudo systemctl status gemini-quant
```

### 2. 安全配置
- 使用专用用户运行系统
- 配置防火墙规则
- 定期更新依赖包
- 备份重要数据

### 3. 监控告警
- 配置系统监控
- 设置邮件/短信告警
- 监控资源使用情况
- 定期检查日志

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 免责声明

本软件仅用于教育和研究目的。使用本软件进行实际交易的风险由用户自行承担。作者不对任何投资损失负责。
