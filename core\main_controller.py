"""
主控制器 - 系统的总指挥
负责系统启动、关闭和各模块的协调
"""

from __future__ import annotations
import sys
import time
import signal
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from .event_bus import EventBus, get_event_bus
from .event_types import (
    SystemStatusEvent, LogEvent, NotificationEvent,
    DataServiceStatusEvent, StrategyStatusEvent
)
from infrastructure.config_manager import ConfigManager
from infrastructure.logger import get_logger, setup_logger


class MainController:
    """
    主控制器
    
    职责：
    1. 系统启动和关闭
    2. 模块生命周期管理
    3. 依赖注入
    4. 事件注册
    5. 异常处理和恢复
    """
    
    def __init__(self):
        # 首先初始化配置
        self._config = ConfigManager()

        # 初始化日志系统
        log_config = self._config.get('logging', {})
        self._logger = setup_logger(
            name='main_controller',
            level=log_config.get('level', 'INFO'),
            log_file=Path(log_config.get('file_path', './logs/quant_trader.log')),
            enable_console=True,
            enable_events=False  # 避免循环依赖
        )

        self._event_bus: Optional[EventBus] = None
        self._modules: Dict[str, Any] = {}
        self._running = False
        self._shutdown_event = threading.Event()

        # 模块启动顺序
        self._startup_order = [
            'infrastructure',
            'data_service',
            'strategy_engine',
            'execution_portfolio',
            'gui'
        ]

        # 注册信号处理器
        self._setup_signal_handlers()
    
    def start(self) -> None:
        """启动系统"""
        if self._running:
            self._logger.warning("系统已经在运行中")
            return
            
        self._logger.info("开始启动量化交易系统...")
        
        try:
            # 1. 初始化事件总线
            self._init_event_bus()
            
            # 2. 发布系统启动事件
            self._publish_system_status("starting", "系统正在启动...")
            
            # 3. 按顺序启动各模块
            self._start_modules()
            
            # 4. 系统启动完成
            self._running = True
            self._publish_system_status("running", "系统启动完成")
            self._logger.info("量化交易系统启动完成")

            # 不自动进入主循环，让外部控制事件循环
            
        except Exception as e:
            self._logger.error(f"系统启动失败: {e}")
            self._publish_system_status("error", f"启动失败: {e}")
            self.stop()
            raise
    
    def stop(self) -> None:
        """停止系统"""
        if not self._running:
            return
            
        self._logger.info("开始关闭系统...")
        self._publish_system_status("stopping", "系统正在关闭...")
        
        try:
            # 1. 设置停止标志
            self._running = False
            self._shutdown_event.set()
            
            # 2. 按相反顺序停止模块
            self._stop_modules()
            
            # 3. 停止事件总线
            if self._event_bus:
                self._event_bus.stop()
                
            # 4. 发布系统停止事件
            self._publish_system_status("stopped", "系统已停止")
            self._logger.info("系统关闭完成")
            
        except Exception as e:
            self._logger.error(f"系统关闭异常: {e}")
    
    def _init_event_bus(self) -> None:
        """初始化事件总线"""
        self._event_bus = get_event_bus()
        
        # 配置事件总线参数
        bus_config = self._config.get('event_bus', {})
        max_workers = bus_config.get('max_workers', 4)
        queue_size = bus_config.get('queue_size', 10000)
        
        # 重新初始化事件总线
        from core.event_bus import init_event_bus
        self._event_bus = init_event_bus(
            max_workers=max_workers,
            queue_size=queue_size
        )
        
        # 启动事件总线
        self._event_bus.start()
        
        # 订阅系统事件
        self._subscribe_system_events()

        # 初始化通知服务
        self._init_notification_service()

        self._logger.info("事件总线初始化完成")

    def _init_notification_service(self) -> None:
        """初始化通知服务"""
        try:
            from infrastructure.notification import NotificationService
            self._notification_service = NotificationService(self._event_bus, self._config.get('notification', {}))
            self._logger.info("通知服务初始化完成")
        except Exception as e:
            self._logger.error(f"通知服务初始化失败: {e}")

    def _subscribe_system_events(self) -> None:
        """订阅系统事件"""
        # 订阅日志事件
        self._event_bus.subscribe(
            "log",
            self._handle_log_event,
            "main_controller_log"
        )
        
        # 订阅数据服务状态事件
        self._event_bus.subscribe(
            "data_service_status",
            self._handle_data_service_status,
            "main_controller_data"
        )
        
        # 订阅策略状态事件
        self._event_bus.subscribe(
            "strategy_status", 
            self._handle_strategy_status,
            "main_controller_strategy"
        )
    
    def _start_modules(self) -> None:
        """启动各模块"""
        for module_name in self._startup_order:
            try:
                self._logger.info(f"启动模块: {module_name}")
                module = self._create_module(module_name)
                
                if hasattr(module, 'start'):
                    module.start()
                    
                self._modules[module_name] = module
                self._logger.info(f"模块 {module_name} 启动成功")
                
            except Exception as e:
                self._logger.error(f"模块 {module_name} 启动失败: {e}")
                raise
    
    def _stop_modules(self) -> None:
        """停止各模块"""
        # 按相反顺序停止
        for module_name in reversed(self._startup_order):
            if module_name in self._modules:
                try:
                    self._logger.info(f"停止模块: {module_name}")
                    module = self._modules[module_name]
                    
                    if hasattr(module, 'stop'):
                        module.stop()
                        
                    del self._modules[module_name]
                    self._logger.info(f"模块 {module_name} 停止成功")
                    
                except Exception as e:
                    self._logger.error(f"模块 {module_name} 停止失败: {e}")
    
    def _create_module(self, module_name: str) -> Any:
        """创建模块实例"""
        if module_name == 'infrastructure':
            # 基础设施模块已经在初始化时创建
            return None
            
        elif module_name == 'data_service':
            from data_service.process import DataServiceProcess
            return DataServiceProcess(self._config)
            
        elif module_name == 'strategy_engine':
            from strategy_ai_engine.live_trader import LiveTrader
            from strategy_ai_engine.strategy_loader import StrategyLoader
            from strategy_ai_engine.ai_optimizer import AIOptimizer

            # 创建策略加载器
            strategy_loader = StrategyLoader(self._config)

            # 创建AI优化器
            ai_optimizer = AIOptimizer(self._config)

            # 创建实盘交易器
            live_trader = LiveTrader(self._event_bus, self._config)

            # 将策略加载器传递给实盘交易器
            if hasattr(live_trader, 'set_strategy_loader'):
                live_trader.set_strategy_loader(strategy_loader)

            return live_trader
            
        elif module_name == 'execution_portfolio':
            from execution_portfolio.portfolio_manager import PortfolioManager
            from execution_portfolio.risk_manager import RiskManager

            # 创建投资组合管理器
            portfolio_manager = PortfolioManager(self._event_bus, self._config)

            # 创建风险管理器
            risk_manager = RiskManager(self._event_bus, self._config)

            # 返回投资组合管理器（风险管理器会自动订阅事件）
            return portfolio_manager
            
        elif module_name == 'gui':
            # GUI模块需要在主线程中创建
            if self._config.get('gui', {}).get('enabled', True):
                # 检查QApplication是否存在
                try:
                    from PySide6.QtWidgets import QApplication
                    if QApplication.instance() is None:
                        self._logger.warning("QApplication未创建，跳过GUI模块")
                        return None
                except ImportError:
                    self._logger.info("PySide6不可用，跳过GUI模块")
                    return None

                from gui.main_window import MainWindow
                return MainWindow(self._event_bus, self._config)
            return None
            
        else:
            raise ValueError(f"未知的模块: {module_name}")
    
    def _main_loop(self) -> None:
        """主循环"""
        self._logger.info("进入主循环")
        
        try:
            # 如果启用了GUI，运行GUI主循环
            if 'gui' in self._modules and self._modules['gui']:
                gui = self._modules['gui']
                if hasattr(gui, 'run'):
                    gui.run()
                else:
                    # 等待关闭信号
                    self._shutdown_event.wait()
            else:
                # 无GUI模式，等待关闭信号
                self._shutdown_event.wait()
                
        except KeyboardInterrupt:
            self._logger.info("收到键盘中断信号")
        except Exception as e:
            self._logger.error(f"主循环异常: {e}")
        finally:
            self.stop()
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self._logger.info(f"收到信号 {signum}")
            self._shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _publish_system_status(self, status: str, message: str = "") -> None:
        """发布系统状态事件"""
        if self._event_bus:
            event = SystemStatusEvent(
                status=status,
                message=message,
                component="main_controller"
            )
            self._event_bus.publish(event)
    
    def _handle_log_event(self, event: LogEvent) -> None:
        """处理日志事件"""
        # 这里可以添加额外的日志处理逻辑
        pass
    
    def _handle_data_service_status(self, event: DataServiceStatusEvent) -> None:
        """处理数据服务状态事件"""
        self._logger.info(f"数据服务状态: {event.service_name} - {event.status}")
        
        # 如果数据服务断开，可以尝试重连
        if event.status == "disconnected":
            self._logger.warning(f"数据服务 {event.service_name} 断开连接")
    
    def _handle_strategy_status(self, event: StrategyStatusEvent) -> None:
        """处理策略状态事件"""
        self._logger.info(f"策略状态: {event.strategy_name} - {event.status}")
    
    def get_module(self, module_name: str) -> Optional[Any]:
        """获取模块实例"""
        return self._modules.get(module_name)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            'running': self._running,
            'modules': list(self._modules.keys()),
            'uptime': time.time() - getattr(self, '_start_time', time.time())
        }
        
        # 添加事件总线统计
        if self._event_bus:
            stats['event_bus'] = self._event_bus.get_stats()
            
        return stats
