"""
日志服务
使用 Python 3.13 的新特性实现高性能日志系统
"""

from __future__ import annotations
import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import threading

from core.event_types import LogEvent
from core.event_bus import get_event_bus


class EventLogHandler(logging.Handler):
    """事件日志处理器 - 将日志发布到事件总线"""
    
    def __init__(self):
        super().__init__()
        self._event_bus = None
        self._lock = threading.Lock()
    
    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录到事件总线"""
        try:
            # 延迟获取事件总线，避免循环依赖
            if self._event_bus is None:
                with self._lock:
                    if self._event_bus is None:
                        try:
                            self._event_bus = get_event_bus()
                        except:
                            return  # 事件总线还未初始化
            
            # 创建日志事件
            log_event = LogEvent(
                level=record.levelname,
                message=record.getMessage(),
                module=record.name,
                extra_data={
                    'filename': record.filename,
                    'lineno': record.lineno,
                    'funcName': record.funcName,
                    'thread': record.thread,
                    'threadName': record.threadName,
                    'process': record.process,
                    'processName': record.processName
                }
            )
            
            # 发布到事件总线
            if self._event_bus:
                self._event_bus.publish(log_event)
                
        except Exception:
            # 避免日志处理器本身出错影响系统
            pass


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器 - 利用 Python 3.13 的改进错误消息"""
    
    # ANSI 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


def setup_logger(
    name: Optional[str] = None,
    level: str = "INFO",
    log_file: Optional[Path] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5,
    enable_console: bool = True,
    enable_events: bool = True,
    enable_colors: bool = True
) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        name: 日志器名称
        level: 日志级别
        log_file: 日志文件路径
        max_file_size: 最大文件大小
        backup_count: 备份文件数量
        enable_console: 是否启用控制台输出
        enable_events: 是否启用事件发布
        enable_colors: 是否启用彩色输出
        
    Returns:
        配置好的日志器
    """
    # 创建日志器
    logger = logging.getLogger(name or 'quant_trader')
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    colored_formatter = ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        
        # 根据是否支持颜色选择格式化器
        if enable_colors and hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            console_handler.setFormatter(colored_formatter)
        else:
            console_handler.setFormatter(formatter)
            
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 解析文件大小
        size_bytes = _parse_size(max_file_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=size_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 事件处理器
    if enable_events:
        event_handler = EventLogHandler()
        event_handler.setLevel(logging.INFO)  # 只发布INFO及以上级别的日志
        logger.addHandler(event_handler)
    
    return logger


def _parse_size(size_str: str) -> int:
    """解析大小字符串"""
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name or 'quant_trader')


# 全局日志器实例
_global_logger: Optional[logging.Logger] = None


def init_logging(config: Dict[str, Any]) -> logging.Logger:
    """初始化全局日志系统"""
    global _global_logger
    
    log_config = config.get('logging', {})
    
    _global_logger = setup_logger(
        name='quant_trader',
        level=log_config.get('level', 'INFO'),
        log_file=Path(log_config.get('file_path', './logs/quant_trader.log')),
        max_file_size=log_config.get('max_file_size', '10MB'),
        backup_count=log_config.get('backup_count', 5),
        enable_console=True,
        enable_events=True,
        enable_colors=True
    )
    
    return _global_logger


class LoggerMixin:
    """日志器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取日志器"""
        if not hasattr(self, '_logger'):
            self._logger = get_logger(self.__class__.__name__)
        return self._logger
