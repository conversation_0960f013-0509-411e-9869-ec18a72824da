[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "gemini-quant"
version = "1.0.0"
description = "个人量化交易系统 - 使用 Python 3.13 构建的事件驱动量化交易平台"
authors = [
    {name = "Gemini Quant Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.13"
keywords = ["quantitative", "trading", "finance", "algorithmic", "backtesting"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Information Analysis",
]

dependencies = [
    "pandas>=2.1.0",
    "numpy>=1.25.0",
    "pyarrow>=14.0.0",
    "PySide6>=6.6.0",
    "QtAwesome>=1.3.0",
    "pyqtgraph>=0.13.0",
    "PyYAML>=6.0.1",
    "requests>=2.31.0",
    "websocket-client>=1.6.0",
    "aiohttp>=3.9.0",
    "scipy>=1.11.0",
    "scikit-learn>=1.3.0",
    "statsmodels>=0.14.0",
    "pandas-ta>=0.3.14b",
    "optuna>=3.4.0",
    "python-dateutil>=2.8.2",
    "pytz>=2023.3",
    "pydantic>=2.5.0",
    "structlog>=23.2.0",
    "matplotlib>=3.8.0",
    "seaborn>=0.13.0",
    "plotly>=5.17.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "flake8>=6.1.0",
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.0",
]

crypto = [
    "ccxt>=4.1.0",
    "python-binance>=1.0.28",
]

stocks = [
    "yfinance>=0.2.0",
    "akshare>=1.12.0",
]

database = [
    "sqlalchemy>=2.0.0",
    "redis>=5.0.0",
]

docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
]

[project.urls]
Homepage = "https://github.com/gemini-quant/gemini-quant"
Documentation = "https://gemini-quant.readthedocs.io"
Repository = "https://github.com/gemini-quant/gemini-quant"
Issues = "https://github.com/gemini-quant/gemini-quant/issues"

[project.scripts]
gemini-quant = "main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "data_service*", "strategy_ai_engine*", "execution_portfolio*", "gui*", "infrastructure*", "strategies*"]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["core", "data_service", "strategy_ai_engine", "execution_portfolio", "gui", "infrastructure", "strategies"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "TA-Lib.*",
    "pandas_ta.*",
    "ccxt.*",
    "yfinance.*",
    "akshare.*",
    "pyqtgraph.*",
    "QtAwesome.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gui: marks tests as GUI tests",
]

[tool.coverage.run]
source = ["core", "data_service", "strategy_ai_engine", "execution_portfolio", "gui", "infrastructure"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
