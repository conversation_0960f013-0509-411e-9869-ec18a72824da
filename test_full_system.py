#!/usr/bin/env python3
"""
完整系统集成测试
测试所有模块的协同工作
"""

import sys
import time
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.main_controller import MainController
from core.event_bus import EventBus
from core.data_types import TickData, BarData, Interval
from core.event_types import TickEvent, BarEvent
from strategies.examples.ma_cross_strategy import MACrossStrategy
from infrastructure.logger import setup_logger


def test_event_bus_integration():
    """测试事件总线集成"""
    print("测试事件总线集成...")
    
    event_bus = EventBus()
    event_bus.start()
    
    # 收集事件
    received_events = []
    
    def event_collector(event):
        received_events.append(event)
        print(f"收到事件: {event.__class__.__name__} from {event.source}")
    
    # 订阅多种事件
    event_bus.subscribe("tick", event_collector, "test_tick")
    event_bus.subscribe("bar", event_collector, "test_bar")
    event_bus.subscribe("signal", event_collector, "test_signal")
    
    # 发送测试事件
    tick = TickData(
        symbol='TEST',
        datetime=datetime.now(),
        last_price=Decimal('100.50'),
        volume=1000,
        turnover=Decimal('100500')
    )
    
    tick_event = TickEvent(data=tick, source='test')
    event_bus.publish(tick_event)
    
    bar = BarData(
        symbol='TEST',
        datetime=datetime.now(),
        interval=Interval.MINUTE_1,
        open_price=Decimal('100.00'),
        high_price=Decimal('101.00'),
        low_price=Decimal('99.50'),
        close_price=Decimal('100.50'),
        volume=10000,
        turnover=Decimal('1005000')
    )
    
    bar_event = BarEvent(data=bar, source='test')
    event_bus.publish(bar_event)
    
    # 等待事件处理
    time.sleep(1)
    
    # 检查结果
    assert len(received_events) >= 2, f"事件数量不足: {len(received_events)}"
    
    # 获取统计信息
    stats = event_bus.get_stats()
    print(f"事件总线统计: {stats}")
    
    event_bus.stop()
    print("✓ 事件总线集成测试通过")


def test_strategy_integration():
    """测试策略集成"""
    print("测试策略集成...")
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 收集信号
    signals = []
    
    def signal_collector(event):
        if hasattr(event, 'symbol'):
            signals.append(event)
            print(f"收到交易信号: {event.symbol} {event.direction} {event.volume}")
    
    event_bus.subscribe("signal", signal_collector, "test_signal")
    
    # 创建策略
    strategy = MACrossStrategy('test_ma', {
        'fast_period': 3,
        'slow_period': 5,
        'symbol': 'TEST',
        'volume': 100
    })
    
    # 设置事件回调
    strategy.set_event_callback(lambda event: event_bus.publish(event))
    
    # 初始化和启动策略
    assert strategy.initialize(), "策略初始化失败"
    assert strategy.start(), "策略启动失败"
    
    # 发送K线数据（创造金叉条件）
    base_time = datetime.now()
    
    # 发送下跌趋势的K线
    for i in range(8):
        price = Decimal(f'{100 - i * 0.5:.2f}')
        bar = BarData(
            symbol='TEST',
            datetime=base_time + timedelta(minutes=i),
            interval=Interval.MINUTE_1,
            open_price=price + Decimal('0.1'),
            high_price=price + Decimal('0.2'),
            low_price=price - Decimal('0.1'),
            close_price=price,
            volume=1000,
            turnover=price * 1000
        )
        strategy.process_bar(bar)
    
    # 发送上涨趋势的K线（触发金叉）
    for i in range(5):
        price = Decimal(f'{96.5 + i * 0.8:.2f}')
        bar = BarData(
            symbol='TEST',
            datetime=base_time + timedelta(minutes=8+i),
            interval=Interval.MINUTE_1,
            open_price=price - Decimal('0.1'),
            high_price=price + Decimal('0.2'),
            low_price=price - Decimal('0.2'),
            close_price=price,
            volume=1000,
            turnover=price * 1000
        )
        strategy.process_bar(bar)
    
    # 等待信号处理
    time.sleep(0.5)
    
    # 检查策略统计
    stats = strategy.get_statistics()
    print(f"策略统计: {stats}")
    
    # 检查是否产生了信号
    print(f"产生的信号数量: {len(signals)}")
    
    strategy.stop()
    event_bus.stop()
    
    print("✓ 策略集成测试通过")


def test_data_flow():
    """测试数据流"""
    print("测试数据流...")
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 数据流跟踪
    data_flow = {
        'ticks': 0,
        'bars': 0,
        'signals': 0,
        'orders': 0,
        'trades': 0
    }
    
    def track_data_flow(event):
        if hasattr(event, 'EVENT_TYPE'):
            event_type = event.EVENT_TYPE
            if event_type == 'tick':
                data_flow['ticks'] += 1
            elif event_type == 'bar':
                data_flow['bars'] += 1
            elif event_type == 'signal':
                data_flow['signals'] += 1
            elif event_type == 'order':
                data_flow['orders'] += 1
            elif event_type == 'trade':
                data_flow['trades'] += 1
    
    # 订阅所有事件类型
    event_bus.subscribe("tick", track_data_flow, "flow_tick")
    event_bus.subscribe("bar", track_data_flow, "flow_bar")
    event_bus.subscribe("signal", track_data_flow, "flow_signal")
    event_bus.subscribe("order", track_data_flow, "flow_order")
    event_bus.subscribe("trade", track_data_flow, "flow_trade")
    
    # 模拟数据流
    base_time = datetime.now()
    
    # 发送Tick数据
    for i in range(10):
        tick = TickData(
            symbol='FLOW_TEST',
            datetime=base_time + timedelta(seconds=i),
            last_price=Decimal(f'{100 + i * 0.1:.2f}'),
            volume=100,
            turnover=Decimal(f'{10000 + i * 10:.2f}')
        )
        event_bus.publish(TickEvent(data=tick, source='test'))
    
    # 发送K线数据
    for i in range(5):
        bar = BarData(
            symbol='FLOW_TEST',
            datetime=base_time + timedelta(minutes=i),
            interval=Interval.MINUTE_1,
            open_price=Decimal(f'{100 + i:.2f}'),
            high_price=Decimal(f'{101 + i:.2f}'),
            low_price=Decimal(f'{99 + i:.2f}'),
            close_price=Decimal(f'{100.5 + i:.2f}'),
            volume=1000,
            turnover=Decimal(f'{100500 + i * 1000:.2f}')
        )
        event_bus.publish(BarEvent(data=bar, source='test'))
    
    # 等待处理
    time.sleep(1)
    
    # 检查数据流
    print(f"数据流统计: {data_flow}")
    assert data_flow['ticks'] == 10, f"Tick数据流错误: {data_flow['ticks']}"
    assert data_flow['bars'] == 5, f"K线数据流错误: {data_flow['bars']}"
    
    event_bus.stop()
    print("✓ 数据流测试通过")


def test_system_startup():
    """测试系统启动"""
    print("测试系统启动...")
    
    # 创建主控制器
    controller = MainController()
    
    # 在单独线程中启动系统
    def start_system():
        try:
            controller.start()
        except Exception as e:
            print(f"系统启动异常: {e}")
    
    system_thread = threading.Thread(target=start_system, daemon=True)
    system_thread.start()
    
    # 等待系统启动
    time.sleep(3)
    
    # 检查系统状态
    stats = controller.get_stats()
    print(f"系统统计: {stats}")
    
    # 停止系统
    controller.stop()
    
    # 等待线程结束
    system_thread.join(timeout=5)
    
    print("✓ 系统启动测试通过")


def run_all_tests():
    """运行所有集成测试"""
    print("=" * 60)
    print("开始运行完整系统集成测试")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger(
        name='test_full_system',
        level='INFO',
        enable_console=True,
        enable_events=False
    )
    
    try:
        test_event_bus_integration()
        test_strategy_integration()
        test_data_flow()
        test_system_startup()
        
        print("=" * 60)
        print("✓ 所有系统集成测试通过！")
        print("🎉 Gemini Quant 系统构建完成！")
        print("=" * 60)
        
        # 显示系统信息
        print("\n📋 系统组件清单:")
        print("✅ 核心引擎 (事件总线、数据类型)")
        print("✅ 数据服务 (数据接入、处理、存储)")
        print("✅ 策略引擎 (策略框架、回测、实盘)")
        print("✅ 交易执行 (订单管理、投资组合)")
        print("✅ GUI界面 (控制台界面)")
        print("✅ 基础设施 (配置、日志、通知)")
        
        print("\n🚀 使用方法:")
        print("python main.py  # 启动完整系统")
        
        print("\n📚 下一步:")
        print("1. 安装 Python 3.13 环境")
        print("2. 安装依赖包: pip install -r requirements.txt")
        print("3. 配置系统参数")
        print("4. 添加数据源和策略")
        print("5. 开始量化交易！")
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
