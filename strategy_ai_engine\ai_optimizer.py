"""
AI优化器
使用Optuna进行策略参数优化
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List, Callable, Tuple
import logging
import multiprocessing as mp
from datetime import datetime, timedelta
import json

try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("Optuna未安装，AI优化器功能不可用")

from core.data_types import BarData, Interval
from strategy_ai_engine.backtester import BacktestEngine


class OptimizationResult:
    """优化结果"""
    
    def __init__(
        self,
        best_params: Dict[str, Any],
        best_value: float,
        study_name: str,
        n_trials: int,
        optimization_time: float
    ):
        self.best_params = best_params
        self.best_value = best_value
        self.study_name = study_name
        self.n_trials = n_trials
        self.optimization_time = optimization_time
        self.timestamp = datetime.now()


class AIOptimizer:
    """
    AI优化器
    
    职责:
    1. 定义优化目标函数
    2. 配置参数搜索空间
    3. 执行优化过程
    4. 分析优化结果
    5. 生成优化报告
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        if not OPTUNA_AVAILABLE:
            self.logger.error("Optuna未安装，AI优化器不可用")
            return
        
        # 优化配置
        self.optimizer_config = config.get('ai_optimizer', {})
        self.enabled = self.optimizer_config.get('enabled', True)
        self.backend = self.optimizer_config.get('backend', 'optuna')
        self.n_trials = self.optimizer_config.get('n_trials', 100)
        self.timeout = self.optimizer_config.get('timeout', 3600)  # 1小时
        
        # 优化历史
        self.optimization_history: List[OptimizationResult] = []
        
        # 当前优化状态
        self.current_study: Optional[optuna.Study] = None
        self.is_optimizing = False
        
    def optimize_strategy(
        self,
        strategy_class: type,
        strategy_config: Dict[str, Any],
        data: List[BarData],
        param_space: Dict[str, Dict[str, Any]],
        objective_metric: str = 'sharpe_ratio',
        study_name: Optional[str] = None
    ) -> Optional[OptimizationResult]:
        """
        优化策略参数
        
        Args:
            strategy_class: 策略类
            strategy_config: 策略基础配置
            data: 历史数据
            param_space: 参数搜索空间
            objective_metric: 优化目标指标
            study_name: 研究名称
            
        Returns:
            OptimizationResult: 优化结果
        """
        if not OPTUNA_AVAILABLE or not self.enabled:
            self.logger.error("AI优化器不可用")
            return None
            
        try:
            self.logger.info("开始策略参数优化...")
            start_time = datetime.now()
            
            # 创建研究
            if study_name is None:
                study_name = f"optimization_{start_time.strftime('%Y%m%d_%H%M%S')}"
            
            # 配置采样器和剪枝器
            sampler = TPESampler(seed=42)
            pruner = MedianPruner(n_startup_trials=5, n_warmup_steps=10)
            
            # 创建优化研究
            direction = 'maximize' if objective_metric in ['sharpe_ratio', 'total_return', 'win_rate'] else 'minimize'
            self.current_study = optuna.create_study(
                study_name=study_name,
                direction=direction,
                sampler=sampler,
                pruner=pruner
            )
            
            # 定义目标函数
            def objective(trial: optuna.Trial) -> float:
                return self._objective_function(
                    trial, strategy_class, strategy_config, data, param_space, objective_metric
                )
            
            # 执行优化
            self.is_optimizing = True
            self.current_study.optimize(
                objective,
                n_trials=self.n_trials,
                timeout=self.timeout,
                callbacks=[self._optimization_callback]
            )
            self.is_optimizing = False
            
            # 获取最佳结果
            best_trial = self.current_study.best_trial
            optimization_time = (datetime.now() - start_time).total_seconds()
            
            result = OptimizationResult(
                best_params=best_trial.params,
                best_value=best_trial.value,
                study_name=study_name,
                n_trials=len(self.current_study.trials),
                optimization_time=optimization_time
            )
            
            # 保存结果
            self.optimization_history.append(result)
            
            self.logger.info(f"优化完成，最佳{objective_metric}: {best_trial.value:.4f}")
            self.logger.info(f"最佳参数: {best_trial.params}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"策略优化失败: {e}")
            self.is_optimizing = False
            return None
    
    def _objective_function(
        self,
        trial: optuna.Trial,
        strategy_class: type,
        strategy_config: Dict[str, Any],
        data: List[BarData],
        param_space: Dict[str, Dict[str, Any]],
        objective_metric: str
    ) -> float:
        """
        优化目标函数
        
        Args:
            trial: Optuna试验对象
            strategy_class: 策略类
            strategy_config: 策略配置
            data: 历史数据
            param_space: 参数空间
            objective_metric: 目标指标
            
        Returns:
            float: 目标值
        """
        try:
            # 根据参数空间建议参数
            suggested_params = {}
            for param_name, param_config in param_space.items():
                param_type = param_config.get('type', 'int')
                
                if param_type == 'int':
                    suggested_params[param_name] = trial.suggest_int(
                        param_name,
                        param_config['low'],
                        param_config['high'],
                        step=param_config.get('step', 1)
                    )
                elif param_type == 'float':
                    suggested_params[param_name] = trial.suggest_float(
                        param_name,
                        param_config['low'],
                        param_config['high'],
                        step=param_config.get('step')
                    )
                elif param_type == 'categorical':
                    suggested_params[param_name] = trial.suggest_categorical(
                        param_name,
                        param_config['choices']
                    )
            
            # 更新策略配置
            test_config = strategy_config.copy()
            test_config.update(suggested_params)
            
            # 执行回测
            backtester = BacktestEngine(self.config)

            # 创建策略实例
            strategy = strategy_class(test_config)

            # 添加策略到回测引擎
            backtester.add_strategy("test_strategy", strategy)

            # 加载数据
            if data:
                symbol = data[0].symbol if data else "TEST"
                backtester.load_data(symbol, data)

                # 运行回测
                start_date = data[0].datetime.date()
                end_date = data[-1].datetime.date()
                result = backtester.run_backtest(start_date, end_date)
            else:
                # 没有数据，返回默认结果
                result = {'metrics': {'sharpe_ratio': 0}}
            
            # 获取目标指标值
            if objective_metric in result.metrics:
                return float(result.metrics[objective_metric])
            else:
                self.logger.warning(f"目标指标 {objective_metric} 不存在，使用夏普比率")
                return float(result.metrics.get('sharpe_ratio', 0))
                
        except Exception as e:
            self.logger.error(f"目标函数执行失败: {e}")
            # 返回一个很差的值
            return -999.0
    
    def _optimization_callback(self, study: optuna.Study, trial: optuna.Trial) -> None:
        """优化回调函数"""
        try:
            if trial.number % 10 == 0:  # 每10次试验记录一次
                self.logger.info(
                    f"试验 {trial.number}: 当前最佳值 = {study.best_value:.4f}, "
                    f"当前值 = {trial.value:.4f}"
                )
        except Exception as e:
            self.logger.error(f"优化回调失败: {e}")
    
    def get_optimization_progress(self) -> Dict[str, Any]:
        """获取优化进度"""
        if not self.current_study or not self.is_optimizing:
            return {'status': 'not_running'}
        
        try:
            return {
                'status': 'running',
                'n_trials': len(self.current_study.trials),
                'best_value': self.current_study.best_value if self.current_study.best_trial else None,
                'best_params': self.current_study.best_params if self.current_study.best_trial else None
            }
        except Exception as e:
            self.logger.error(f"获取优化进度失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def stop_optimization(self) -> bool:
        """停止优化"""
        try:
            if self.current_study and self.is_optimizing:
                # Optuna没有直接的停止方法，这里设置标志
                self.is_optimizing = False
                self.logger.info("优化停止请求已发送")
                return True
            return False
        except Exception as e:
            self.logger.error(f"停止优化失败: {e}")
            return False
    
    def analyze_optimization_result(self, result: OptimizationResult) -> Dict[str, Any]:
        """分析优化结果"""
        if not OPTUNA_AVAILABLE:
            return {'error': 'Optuna不可用'}
        
        try:
            analysis = {
                'best_params': result.best_params,
                'best_value': result.best_value,
                'n_trials': result.n_trials,
                'optimization_time': result.optimization_time,
                'trials_per_second': result.n_trials / result.optimization_time if result.optimization_time > 0 else 0
            }
            
            # 如果有当前研究，添加更多分析
            if self.current_study and self.current_study.study_name == result.study_name:
                # 参数重要性
                try:
                    importance = optuna.importance.get_param_importances(self.current_study)
                    analysis['param_importance'] = importance
                except:
                    pass
                
                # 试验历史
                trials_data = []
                for trial in self.current_study.trials:
                    if trial.value is not None:
                        trials_data.append({
                            'number': trial.number,
                            'value': trial.value,
                            'params': trial.params,
                            'state': trial.state.name
                        })
                analysis['trials_history'] = trials_data
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析优化结果失败: {e}")
            return {'error': str(e)}
    
    def save_optimization_result(self, result: OptimizationResult, file_path: str) -> bool:
        """保存优化结果"""
        try:
            result_data = {
                'best_params': result.best_params,
                'best_value': result.best_value,
                'study_name': result.study_name,
                'n_trials': result.n_trials,
                'optimization_time': result.optimization_time,
                'timestamp': result.timestamp.isoformat()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"优化结果已保存到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存优化结果失败: {e}")
            return False
    
    def load_optimization_result(self, file_path: str) -> Optional[OptimizationResult]:
        """加载优化结果"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            result = OptimizationResult(
                best_params=result_data['best_params'],
                best_value=result_data['best_value'],
                study_name=result_data['study_name'],
                n_trials=result_data['n_trials'],
                optimization_time=result_data['optimization_time']
            )
            result.timestamp = datetime.fromisoformat(result_data['timestamp'])
            
            self.logger.info(f"优化结果已从 {file_path} 加载")
            return result
            
        except Exception as e:
            self.logger.error(f"加载优化结果失败: {e}")
            return None
    
    def get_optimization_history(self) -> List[OptimizationResult]:
        """获取优化历史"""
        return self.optimization_history.copy()
    
    def clear_optimization_history(self) -> None:
        """清空优化历史"""
        self.optimization_history.clear()
        self.logger.info("优化历史已清空")
