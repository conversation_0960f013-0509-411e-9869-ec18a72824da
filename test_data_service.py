#!/usr/bin/env python3
"""
数据服务模块测试
"""

import sys
import time
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from data_service.connectors.csv_connector import CSVConnector
from data_service.processor import DataProcessor, BarGenerator
from data_service.storage import DataStorage
from core.data_types import Interval
from core.event_types import TickEvent, BarEvent
from infrastructure.logger import setup_logger


def test_csv_connector():
    """测试CSV连接器"""
    print("测试CSV连接器...")
    
    config = {
        'data_path': './data/csv'
    }
    
    connector = CSVConnector('csv_test', config)
    
    # 测试连接
    assert connector.connect(), "连接失败"
    
    # 测试订阅
    assert connector.subscribe('AAPL'), "订阅AAPL失败"
    assert connector.subscribe('TSLA'), "订阅TSLA失败"
    
    # 测试获取历史数据
    start_time = datetime(2024, 1, 2, 9, 30)
    end_time = datetime(2024, 1, 2, 9, 50)
    
    aapl_bars = connector.get_historical_bars('AAPL', start_time, end_time, '1m')
    assert len(aapl_bars) > 0, "未获取到AAPL历史数据"
    
    tsla_bars = connector.get_historical_bars('TSLA', start_time, end_time, '1m')
    assert len(tsla_bars) > 0, "未获取到TSLA历史数据"
    
    print(f"✓ CSV连接器测试通过，AAPL: {len(aapl_bars)}条, TSLA: {len(tsla_bars)}条")
    
    # 测试断开连接
    assert connector.disconnect(), "断开连接失败"


def test_bar_generator():
    """测试K线合成器"""
    print("测试K线合成器...")
    
    generated_bars = []
    
    def bar_callback(bar):
        generated_bars.append(bar)
        print(f"生成K线: {bar.symbol} {bar.datetime} O:{bar.open_price} C:{bar.close_price}")
    
    generator = BarGenerator('TEST', Interval.MINUTE_1, bar_callback)
    
    # 模拟Tick数据
    from core.data_types import TickData
    from decimal import Decimal
    
    base_time = datetime(2024, 1, 2, 9, 30)
    
    # 第一分钟的Tick
    for i in range(5):
        tick = TickData(
            symbol='TEST',
            datetime=base_time.replace(second=i*10),
            last_price=Decimal(f'100.{i:02d}'),
            volume=100,
            turnover=Decimal(f'10000.{i:02d}')
        )
        generator.update_tick(tick)
    
    # 第二分钟的Tick（应该触发K线生成）
    tick = TickData(
        symbol='TEST',
        datetime=base_time.replace(minute=31, second=0),
        last_price=Decimal('101.00'),
        volume=100,
        turnover=Decimal('10100.00')
    )
    generator.update_tick(tick)
    
    # 强制完成当前K线
    generator.force_complete_bar()
    
    assert len(generated_bars) == 2, f"应该生成2根K线，实际生成{len(generated_bars)}根"
    print("✓ K线合成器测试通过")


def test_data_processor():
    """测试数据处理器"""
    print("测试数据处理器...")
    
    received_events = []
    
    def event_callback(event):
        received_events.append(event)
    
    processor = DataProcessor(event_callback)
    
    # 注册K线合成器
    processor.register_bar_generator('TEST', Interval.MINUTE_1)
    processor.register_bar_generator('TEST', Interval.MINUTE_5)
    
    # 模拟处理Tick数据
    from core.data_types import TickData
    from decimal import Decimal
    
    base_time = datetime(2024, 1, 2, 9, 30)
    
    for i in range(10):
        tick = TickData(
            symbol='TEST',
            datetime=base_time.replace(second=i*6),
            last_price=Decimal(f'100.{i:02d}'),
            volume=100,
            turnover=Decimal(f'10000.{i:02d}')
        )
        processor.process_tick(tick)
    
    # 触发新分钟
    tick = TickData(
        symbol='TEST',
        datetime=base_time.replace(minute=31),
        last_price=Decimal('101.00'),
        volume=100,
        turnover=Decimal('10100.00')
    )
    processor.process_tick(tick)
    
    stats = processor.get_statistics()
    print(f"处理统计: {stats}")
    
    assert stats['processed_ticks'] == 11, "处理的Tick数量不正确"
    print("✓ 数据处理器测试通过")


def test_data_storage():
    """测试数据存储"""
    print("测试数据存储...")
    
    storage = DataStorage('./test_data', 'feather')
    
    # 创建测试数据
    from core.data_types import TickData, BarData
    from decimal import Decimal
    
    test_date = date(2024, 1, 2)
    base_time = datetime(2024, 1, 2, 9, 30)
    
    # 测试Tick数据存储
    ticks = []
    for i in range(5):
        tick = TickData(
            symbol='TEST',
            datetime=base_time.replace(second=i*10),
            last_price=Decimal(f'100.{i:02d}'),
            volume=100,
            turnover=Decimal(f'10000.{i:02d}')
        )
        ticks.append(tick)
    
    assert storage.save_tick_data(ticks, async_write=False), "保存Tick数据失败"
    
    # 测试K线数据存储
    bars = []
    for i in range(3):
        bar = BarData(
            symbol='TEST',
            datetime=base_time.replace(minute=30+i),
            interval=Interval.MINUTE_1,
            open_price=Decimal(f'100.{i:02d}'),
            high_price=Decimal(f'100.{i+1:02d}'),
            low_price=Decimal(f'99.{i:02d}'),
            close_price=Decimal(f'100.{i+1:02d}'),
            volume=1000,
            turnover=Decimal(f'100000.{i:02d}')
        )
        bars.append(bar)
    
    assert storage.save_bar_data(bars, async_write=False), "保存K线数据失败"
    
    # 测试数据读取
    loaded_ticks = storage.load_tick_data('TEST', test_date, test_date)
    assert len(loaded_ticks) == 5, f"读取的Tick数据数量不正确: {len(loaded_ticks)}"
    
    loaded_bars = storage.load_bar_data('TEST', Interval.MINUTE_1, test_date, test_date)
    assert len(loaded_bars) == 3, f"读取的K线数据数量不正确: {len(loaded_bars)}"
    
    # 清理测试数据
    import shutil
    shutil.rmtree('./test_data', ignore_errors=True)
    
    storage.close()
    print("✓ 数据存储测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行数据服务模块测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger(
        name='test_data_service',
        level='INFO',
        enable_console=True,
        enable_events=False
    )
    
    try:
        test_csv_connector()
        test_bar_generator()
        test_data_processor()
        test_data_storage()
        
        print("=" * 50)
        print("✓ 所有数据服务测试通过！")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
