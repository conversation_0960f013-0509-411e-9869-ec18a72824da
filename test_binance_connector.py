#!/usr/bin/env python3
"""
测试Binance数据连接器功能
验证连接、获取品种信息、历史数据等功能
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from data_service.connectors.binance_connector import BinanceConnector


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_binance_connection():
    """测试Binance连接"""
    print("=" * 60)
    print("测试Binance连接器")
    print("=" * 60)
    
    # 配置（使用公共API，无需密钥）
    config = {
        'api_key': '',
        'api_secret': '',
        'testnet': False,
        'base_url': ''
    }
    
    try:
        # 创建连接器
        connector = BinanceConnector('binance_test', config)
        print("✓ Binance连接器创建成功")
        
        # 测试连接
        if connector.connect():
            print("✓ 连接Binance成功")
        else:
            print("✗ 连接Binance失败")
            return False
        
        return connector
        
    except Exception as e:
        print(f"✗ 创建连接器失败: {e}")
        return None


def test_get_symbols(connector):
    """测试获取交易品种"""
    print("\n" + "-" * 40)
    print("测试获取交易品种")
    print("-" * 40)
    
    try:
        # 获取所有品种
        symbols = connector.get_all_symbols()
        print(f"✓ 获取到 {len(symbols)} 个交易品种")
        
        # 显示前10个USDT交易对
        usdt_symbols = [s for s in symbols if s['quoteAsset'] == 'USDT'][:10]
        print("\n前10个USDT交易对:")
        for symbol in usdt_symbols:
            print(f"  {symbol['symbol']}: {symbol['baseAsset']}/{symbol['quoteAsset']}")
        
        return usdt_symbols
        
    except Exception as e:
        print(f"✗ 获取交易品种失败: {e}")
        return []


def test_get_symbol_info(connector, symbol='BTCUSDT'):
    """测试获取品种信息"""
    print(f"\n" + "-" * 40)
    print(f"测试获取品种信息: {symbol}")
    print("-" * 40)
    
    try:
        symbol_info = connector.get_symbol_info(symbol)
        if symbol_info:
            print(f"✓ 获取品种信息成功:")
            print(f"  品种: {symbol_info['symbol']}")
            print(f"  名称: {symbol_info['name']}")
            print(f"  交易所: {symbol_info['exchange']}")
            print(f"  最小价格变动: {symbol_info['price_tick']}")
            print(f"  最小交易量: {symbol_info['min_volume']}")
        else:
            print(f"✗ 获取品种信息失败")
            
    except Exception as e:
        print(f"✗ 获取品种信息异常: {e}")


def test_get_24hr_ticker(connector):
    """测试获取24小时价格统计"""
    print(f"\n" + "-" * 40)
    print("测试获取24小时价格统计")
    print("-" * 40)
    
    try:
        # 获取所有24小时统计
        tickers = connector.get_24hr_ticker()
        print(f"✓ 获取到 {len(tickers)} 个品种的24小时统计")
        
        # 显示前5个USDT交易对的价格信息
        usdt_tickers = [t for t in tickers if t['symbol'].endswith('USDT')][:5]
        print("\n前5个USDT交易对价格信息:")
        for ticker in usdt_tickers:
            symbol = ticker['symbol']
            price = float(ticker['lastPrice'])
            change_percent = float(ticker['priceChangePercent'])
            volume = float(ticker['volume'])
            print(f"  {symbol}: ${price:.4f} ({change_percent:+.2f}%) 成交量: {volume:,.0f}")
            
    except Exception as e:
        print(f"✗ 获取24小时统计失败: {e}")


def test_get_historical_data(connector, symbol='BTCUSDT'):
    """测试获取历史数据"""
    print(f"\n" + "-" * 40)
    print(f"测试获取历史数据: {symbol}")
    print("-" * 40)
    
    try:
        # 获取最近1天的1小时K线数据
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)
        
        bars = connector.get_historical_bars(
            symbol=symbol,
            start_time=start_time,
            end_time=end_time,
            interval='1h'
        )
        
        if bars:
            print(f"✓ 获取到 {len(bars)} 条K线数据")
            
            # 显示最近3条K线
            print(f"\n最近3条K线数据:")
            for bar in bars[-3:]:
                print(f"  {bar.datetime}: O:{bar.open_price} H:{bar.high_price} "
                      f"L:{bar.low_price} C:{bar.close_price} V:{bar.volume}")
        else:
            print(f"✗ 未获取到历史数据")
            
    except Exception as e:
        print(f"✗ 获取历史数据失败: {e}")


def test_subscription(connector, symbol='BTCUSDT'):
    """测试订阅功能"""
    print(f"\n" + "-" * 40)
    print(f"测试订阅功能: {symbol}")
    print("-" * 40)
    
    try:
        # 订阅品种
        if connector.subscribe(symbol):
            print(f"✓ 订阅 {symbol} 成功")
            
            # 检查订阅状态
            subscribed = connector.get_subscribed_symbols()
            print(f"✓ 当前订阅品种: {subscribed}")
            
            # 取消订阅
            if connector.unsubscribe(symbol):
                print(f"✓ 取消订阅 {symbol} 成功")
            else:
                print(f"✗ 取消订阅 {symbol} 失败")
        else:
            print(f"✗ 订阅 {symbol} 失败")
            
    except Exception as e:
        print(f"✗ 测试订阅功能失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("🚀 开始测试Binance数据连接器")
    
    # 测试连接
    connector = test_binance_connection()
    if not connector:
        print("\n❌ 连接测试失败，退出")
        return
    
    try:
        # 测试获取品种列表
        symbols = test_get_symbols(connector)
        
        # 测试获取品种信息
        test_symbol = 'BTCUSDT'
        test_get_symbol_info(connector, test_symbol)
        
        # 测试获取24小时统计
        test_get_24hr_ticker(connector)
        
        # 测试获取历史数据
        test_get_historical_data(connector, test_symbol)
        
        # 测试订阅功能
        test_subscription(connector, test_symbol)
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        # 断开连接
        if connector:
            connector.disconnect()
            print("🔌 已断开Binance连接")


if __name__ == "__main__":
    main()
