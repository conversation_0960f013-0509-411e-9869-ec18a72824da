# Gemini Quant 快速入门指南

## 🚀 5分钟快速体验

### 1. 环境准备
```bash
# 确保Python 3.13已安装
python --version  # 应显示 Python 3.13.x

# 克隆项目（或解压项目文件）
cd Gemini_quant

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装基础依赖
pip install pandas numpy pyarrow pydantic PyYAML python-dateutil pytz structlog typing_extensions
```

### 2. 验证安装
```bash
# 运行基础测试
python test_startup.py

# 如果看到 "✓ 所有测试通过！基础架构工作正常" 说明安装成功
```

### 3. 启动系统
```bash
# 启动完整系统
python main.py

# 系统会显示控制台界面，按 Ctrl+C 退出
```

## 📊 第一个策略

### 1. 准备测试数据
创建测试数据文件 `data/csv/TEST_1m.csv`：
```csv
datetime,open,high,low,close,volume,turnover
2024-01-02 09:30:00,100.00,100.50,99.80,100.20,1000,100200
2024-01-02 09:31:00,100.20,100.80,100.00,100.60,1200,120720
2024-01-02 09:32:00,100.60,101.00,100.40,100.90,1100,110990
2024-01-02 09:33:00,100.90,101.20,100.70,101.00,1300,131300
2024-01-02 09:34:00,101.00,101.50,100.80,101.30,1400,141820
```

### 2. 运行示例策略
```bash
# 测试移动平均策略
python test_strategy_engine.py

# 查看策略运行结果
```

### 3. 创建自己的策略
创建文件 `strategies/my_first_strategy.py`：
```python
from strategies.base_strategy import BaseStrategy
from core.data_types import BarData
from decimal import Decimal

class MyFirstStrategy(BaseStrategy):
    def on_init(self):
        self.subscribed_symbols = ['TEST']
        self.last_price = None
        self.position = 0
    
    def on_start(self):
        self.logger.info("我的第一个策略启动！")
    
    def on_stop(self):
        self.logger.info("策略停止")
    
    def on_tick(self, tick):
        pass
    
    def on_bar(self, bar):
        current_price = bar.close_price
        
        # 简单的价格突破策略
        if self.last_price is not None:
            # 价格上涨超过1%且没有持仓时买入
            if (current_price > self.last_price * Decimal('1.01') and 
                self.position == 0):
                self.buy(bar.symbol, 100)
                self.position = 100
                self.logger.info(f"买入信号: {bar.symbol} @ {current_price}")
            
            # 价格下跌超过1%且有持仓时卖出
            elif (current_price < self.last_price * Decimal('0.99') and 
                  self.position > 0):
                self.sell(bar.symbol, 100)
                self.position = 0
                self.logger.info(f"卖出信号: {bar.symbol} @ {current_price}")
        
        self.last_price = current_price
```

## 🔄 回测你的策略

创建回测脚本 `my_backtest.py`：
```python
#!/usr/bin/env python3
import sys
from pathlib import Path
from datetime import date, datetime
from decimal import Decimal

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from strategy_ai_engine.backtester import BacktestEngine
from strategies.my_first_strategy import MyFirstStrategy
from core.data_types import BarData, Interval

# 创建回测引擎
engine = BacktestEngine(initial_capital=Decimal('100000'))

# 添加策略
strategy = MyFirstStrategy('my_first_strategy')
engine.add_strategy(strategy)

# 创建测试数据
test_bars = []
base_time = datetime(2024, 1, 2, 9, 30)

for i in range(100):
    # 模拟价格波动
    base_price = 100 + i * 0.1 + (i % 10) * 0.2
    bar = BarData(
        symbol='TEST',
        datetime=base_time.replace(minute=30 + i),
        interval=Interval.MINUTE_1,
        open_price=Decimal(f'{base_price:.2f}'),
        high_price=Decimal(f'{base_price + 0.5:.2f}'),
        low_price=Decimal(f'{base_price - 0.3:.2f}'),
        close_price=Decimal(f'{base_price + 0.2:.2f}'),
        volume=1000,
        turnover=Decimal(f'{(base_price + 0.2) * 1000:.2f}')
    )
    test_bars.append(bar)

# 加载数据
engine.load_data('TEST', test_bars)

# 运行回测
result = engine.run_backtest(date(2024, 1, 2), date(2024, 1, 2))

# 显示结果
print("=" * 50)
print("回测结果")
print("=" * 50)
print(f"初始资金: ${result['initial_capital']:,.2f}")
print(f"最终余额: ${result['final_balance']:,.2f}")
print(f"总收益率: {result['total_return']:.2%}")
print(f"交易次数: {result['total_trades']}")
print(f"胜率: {result['win_rate']:.2%}")
print(f"最大回撤: {result['max_drawdown']:.2%}")
print("=" * 50)
```

运行回测：
```bash
python my_backtest.py
```

## 📈 实时数据接入

### 1. 配置CSV数据源
编辑 `config/user_config.yaml`：
```yaml
data_service:
  storage_path: "./data"
  connectors:
    csv:
      enabled: true
      data_path: "./data/csv"

execution:
  simulation_mode: true
  initial_capital: 100000.0

gui:
  enabled: true
```

### 2. 添加更多测试数据
在 `data/csv/` 目录下添加更多股票数据文件：
- `AAPL_1m.csv`
- `TSLA_1m.csv`
- `MSFT_1m.csv`

### 3. 运行实时模拟
```bash
python main.py
```

系统会：
1. 加载CSV数据
2. 启动策略引擎
3. 模拟实时交易
4. 显示控制台界面

## 🛠️ 系统配置

### 基础配置
创建 `config/user_config.yaml`：
```yaml
# 系统配置
system:
  name: "我的量化系统"
  debug: false

# 日志配置
logging:
  level: "INFO"
  file_path: "./logs/quant_trader.log"
  max_file_size: "50MB"
  backup_count: 5

# 数据服务配置
data_service:
  storage_path: "./data"
  storage_type: "feather"  # 或 "parquet"
  connectors:
    csv:
      enabled: true
      data_path: "./data/csv"

# 策略引擎配置
strategy_engine:
  max_strategies: 10
  strategy_path: "./strategies"

# 交易执行配置
execution:
  simulation_mode: true
  initial_capital: 100000.0
  commission_rate: 0.0003

# 风险管理配置
risk_management:
  max_position_size: 0.1    # 最大单个持仓比例 10%
  max_daily_loss: 0.05      # 最大日损失 5%
  max_drawdown: 0.2         # 最大回撤 20%

# GUI配置
gui:
  enabled: true
  theme: "dark"
  refresh_interval: 2.0
```

### 高级配置
```yaml
# 性能优化
performance:
  enable_multiprocessing: true
  max_workers: 4
  cache_size: 10000

# 通知配置
notifications:
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_password"
  
  webhook:
    enabled: false
    url: "https://hooks.slack.com/services/..."
```

## 🔍 监控和调试

### 1. 查看日志
```bash
# 实时查看日志
tail -f logs/quant_trader.log

# 搜索特定内容
grep "ERROR" logs/quant_trader.log
grep "策略" logs/quant_trader.log
```

### 2. 调试模式
修改配置启用调试：
```yaml
system:
  debug: true

logging:
  level: "DEBUG"
```

### 3. 性能监控
```bash
# 内存使用监控
python -c "
import psutil
import time
while True:
    print(f'内存使用: {psutil.virtual_memory().percent}%')
    time.sleep(5)
"

# 系统资源监控
htop  # Linux/macOS
# 或在Windows任务管理器中查看
```

## 📚 学习资源

### 1. 代码示例
- `strategies/examples/ma_cross_strategy.py` - 移动平均策略
- `test_*.py` - 各种测试用例
- `main.py` - 系统入口

### 2. 文档
- `README.md` - 项目概述
- `API.md` - API文档
- `DEPLOYMENT.md` - 部署指南

### 3. 扩展开发
```python
# 自定义技术指标
def rsi(prices, period=14):
    """相对强弱指标"""
    deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
    gains = [d if d > 0 else 0 for d in deltas]
    losses = [-d if d < 0 else 0 for d in deltas]
    
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

# 在策略中使用
class RSIStrategy(BaseStrategy):
    def on_bar(self, bar):
        bars = self.get_bars(bar.symbol, 20)
        if len(bars) >= 14:
            prices = [float(b.close_price) for b in bars]
            rsi_value = rsi(prices)
            
            if rsi_value < 30:  # 超卖
                self.buy(bar.symbol, 100)
            elif rsi_value > 70:  # 超买
                self.sell(bar.symbol, 100)
```

## ❓ 常见问题

### Q: 如何添加新的数据源？
A: 继承 `BaseConnector` 类并实现相应方法，然后在配置中启用。

### Q: 策略运行但没有交易信号？
A: 检查策略逻辑、数据质量和日志输出，确保条件能够触发。

### Q: 如何优化策略参数？
A: 使用回测引擎测试不同参数组合，或集成AI优化器。

### Q: 系统占用内存过多？
A: 减少数据缓存大小，限制策略数量，使用更高效的数据格式。

### Q: 如何连接真实交易所？
A: 实现对应的券商接口，替换模拟交易模式。

## 🎯 下一步

1. **熟悉系统**: 运行所有测试，了解各模块功能
2. **开发策略**: 基于你的交易思路开发策略
3. **回测验证**: 使用历史数据验证策略效果
4. **参数优化**: 优化策略参数提高收益
5. **风险控制**: 完善风险管理机制
6. **实盘部署**: 连接真实交易接口

## 📞 获取帮助

- 📖 查看完整文档: `README.md`
- 🔧 API参考: `API.md`
- 🚀 部署指南: `DEPLOYMENT.md`
- 💬 提交问题: GitHub Issues
- 📧 邮件联系: <EMAIL>

祝你量化交易成功！🎉
