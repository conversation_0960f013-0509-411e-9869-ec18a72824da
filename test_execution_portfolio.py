#!/usr/bin/env python3
"""
交易执行与投资组合管理测试
"""

import sys
import time
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from execution_portfolio.portfolio_manager import PortfolioManager, Position, Account, RiskManager
from execution_portfolio.order_executor import OrderExecutor, Order, SimulatedBroker
from core.data_types import (
    OrderData, TradeData, PositionData, AccountData,
    Direction, OrderType, OrderStatus, TickData, BarData, Interval
)
from core.event_types import SignalEvent, TradeEvent, TickEvent
from core.event_bus import EventBus
from infrastructure.logger import setup_logger


def test_position():
    """测试持仓管理"""
    print("测试持仓管理...")
    
    # 创建持仓
    position = Position('AAPL', Direction.LONG)
    
    # 测试开仓
    trade1 = TradeData(
        symbol='AAPL',
        trade_id='T001',
        order_id='O001',
        direction=Direction.LONG,
        volume=100,
        price=Decimal('150.00'),
        datetime=datetime.now(),
        commission=Decimal('4.50')
    )
    
    position.add_trade(trade1)
    assert position.volume == 100, f"持仓数量错误: {position.volume}"
    assert position.avg_price == Decimal('150.00'), f"平均价格错误: {position.avg_price}"
    
    # 测试加仓
    trade2 = TradeData(
        symbol='AAPL',
        trade_id='T002',
        order_id='O002',
        direction=Direction.LONG,
        volume=50,
        price=Decimal('155.00'),
        datetime=datetime.now(),
        commission=Decimal('2.25')
    )
    
    position.add_trade(trade2)
    assert position.volume == 150, f"加仓后数量错误: {position.volume}"
    
    # 计算期望的平均价格: (150*100 + 155*50) / 150 = 151.67
    expected_avg = (Decimal('150.00') * 100 + Decimal('155.00') * 50) / 150
    assert abs(position.avg_price - expected_avg) < Decimal('0.01'), f"加仓后平均价格错误: {position.avg_price}"
    
    # 测试市价更新
    position.update_market_price(Decimal('160.00'))
    expected_pnl = (Decimal('160.00') - position.avg_price) * 150
    assert abs(position.pnl - expected_pnl) < Decimal('0.01'), f"盈亏计算错误: {position.pnl}"
    
    print("✓ 持仓管理测试通过")


def test_account():
    """测试账户管理"""
    print("测试账户管理...")
    
    # 创建账户
    account = Account('test_account', Decimal('100000'))
    
    # 测试初始状态
    assert account.balance == Decimal('100000'), "初始余额错误"
    assert account.available == Decimal('100000'), "初始可用资金错误"
    assert account.frozen == Decimal('0'), "初始冻结资金错误"
    
    # 测试资金冻结
    assert account.freeze_funds(Decimal('10000')), "资金冻结失败"
    assert account.available == Decimal('90000'), "冻结后可用资金错误"
    assert account.frozen == Decimal('10000'), "冻结资金错误"
    
    # 测试资金解冻
    account.unfreeze_funds(Decimal('5000'))
    assert account.available == Decimal('95000'), "解冻后可用资金错误"
    assert account.frozen == Decimal('5000'), "解冻后冻结资金错误"
    
    # 测试余额更新
    account.update_balance(Decimal('-1000'))  # 扣除手续费
    assert account.balance == Decimal('99000'), "余额更新错误"
    
    print("✓ 账户管理测试通过")


def test_risk_manager():
    """测试风险管理"""
    print("测试风险管理...")
    
    # 创建风险管理器
    config = {
        'max_position_size': 0.1,
        'max_daily_loss': 0.05,
        'max_drawdown': 0.2
    }
    
    risk_manager = RiskManager(config)
    
    # 创建测试账户
    account = Account('test', Decimal('100000'))
    risk_manager.set_daily_start_balance(Decimal('100000'))
    
    # 测试订单风险检查（小额订单）
    order = OrderData(
        symbol='AAPL',
        order_id='O001',
        direction=Direction.LONG,
        order_type=OrderType.MARKET,
        volume=50,  # 减少数量
        price=Decimal('100.00'),  # 减少价格
        status=OrderStatus.PENDING,
        datetime=datetime.now()
    )
    
    # 正常订单应该通过
    can_trade, reason = risk_manager.check_order_risk(order, account, {})
    assert can_trade, f"正常订单被拒绝: {reason}"
    
    # 测试账户风险检查
    warnings = risk_manager.check_account_risk(account)
    assert len(warnings) == 0, f"正常账户产生风险警告: {warnings}"
    
    # 模拟大幅亏损
    account.update_balance(Decimal('-10000'))  # 亏损10%
    warnings = risk_manager.check_account_risk(account)
    assert len(warnings) > 0, "大幅亏损未产生风险警告"
    
    print("✓ 风险管理测试通过")


def test_simulated_broker():
    """测试模拟券商"""
    print("测试模拟券商...")
    
    broker = SimulatedBroker()
    
    # 设置市场价格
    broker.set_market_price('AAPL', Decimal('150.00'))
    
    # 创建市价单
    order_data = OrderData(
        symbol='AAPL',
        order_id='O001',
        direction=Direction.LONG,
        order_type=OrderType.MARKET,
        volume=100,
        price=Decimal('0'),
        status=OrderStatus.PENDING,
        datetime=datetime.now()
    )
    
    order = Order(order_data)
    
    # 提交订单
    assert broker.submit_order(order), "市价单提交失败"
    assert order.data.status == OrderStatus.FILLED, "市价单未立即成交"
    assert len(order.trades) == 1, "市价单成交记录错误"
    
    # 检查成交价格（应该包含滑点）
    trade = order.trades[0]
    expected_price = Decimal('150.00') * (1 + broker.slippage)
    assert abs(trade.price - expected_price) < Decimal('0.01'), f"成交价格错误: {trade.price}"
    
    print("✓ 模拟券商测试通过")


def test_order_executor():
    """测试订单执行器"""
    print("测试订单执行器...")
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 创建配置
    config = {
        'execution': {
            'simulation_mode': True,
            'commission_rate': 0.0003
        }
    }
    
    # 创建订单执行器
    executor = OrderExecutor(event_bus, config)
    
    # 收集事件
    received_events = []
    def event_collector(event):
        received_events.append(event)
    
    event_bus.subscribe("order", event_collector)
    event_bus.subscribe("trade", event_collector)
    
    # 启动执行器
    executor.start()
    
    # 发送价格更新
    tick = TickData(
        symbol='AAPL',
        datetime=datetime.now(),
        last_price=Decimal('150.00'),
        volume=1000,
        turnover=Decimal('150000')
    )
    
    tick_event = TickEvent(data=tick)
    event_bus.publish(tick_event)
    
    # 发送交易信号
    signal = SignalEvent(
        symbol='AAPL',
        direction='LONG',
        volume=100,
        strategy_name='test_strategy'
    )
    
    event_bus.publish(signal)
    
    # 等待处理
    time.sleep(0.5)
    
    # 检查结果
    assert len(received_events) >= 2, f"事件数量不足: {len(received_events)}"
    
    # 检查统计信息
    stats = executor.get_statistics()
    assert stats['total_orders'] >= 1, f"订单数量错误: {stats['total_orders']}"
    
    # 停止执行器
    executor.stop()
    event_bus.stop()
    
    print("✓ 订单执行器测试通过")


def test_portfolio_manager():
    """测试投资组合管理器"""
    print("测试投资组合管理器...")
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 创建配置
    config = {
        'execution': {
            'initial_capital': 100000,
            'commission_rate': 0.0003
        },
        'risk_management': {
            'max_position_size': 0.1,
            'max_daily_loss': 0.05,
            'max_drawdown': 0.2
        }
    }
    
    # 创建投资组合管理器
    portfolio = PortfolioManager(event_bus, config)
    
    # 收集事件
    received_events = []
    def event_collector(event):
        received_events.append(event)
    
    event_bus.subscribe("position_update", event_collector)
    event_bus.subscribe("account_update", event_collector)
    
    # 启动管理器
    portfolio.start()
    
    # 模拟成交
    trade = TradeData(
        symbol='AAPL',
        trade_id='T001',
        order_id='O001',
        direction=Direction.LONG,
        volume=100,
        price=Decimal('150.00'),
        datetime=datetime.now(),
        commission=Decimal('4.50')
    )
    
    trade_event = TradeEvent(data=trade)
    event_bus.publish(trade_event)
    
    # 发送价格更新
    tick = TickData(
        symbol='AAPL',
        datetime=datetime.now(),
        last_price=Decimal('155.00'),
        volume=1000,
        turnover=Decimal('155000')
    )
    
    tick_event = TickEvent(data=tick)
    event_bus.publish(tick_event)
    
    # 等待处理
    time.sleep(0.5)
    
    # 检查账户信息
    account_info = portfolio.get_account_info()
    assert account_info is not None, "账户信息为空"
    assert account_info.balance < Decimal('100000'), "账户余额未扣除手续费"
    
    # 检查持仓信息
    positions = portfolio.get_positions()
    assert len(positions) == 1, f"持仓数量错误: {len(positions)}"
    
    position = positions[0]
    assert position.symbol == 'AAPL', "持仓品种错误"
    assert position.volume == 100, "持仓数量错误"
    
    # 检查统计信息
    stats = portfolio.get_statistics()
    assert stats['total_trades'] == 1, "交易次数错误"
    assert stats['active_positions'] == 1, "活跃持仓数量错误"
    
    # 停止管理器
    portfolio.stop()
    event_bus.stop()
    
    print("✓ 投资组合管理器测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行交易执行与投资组合管理测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger(
        name='test_execution_portfolio',
        level='INFO',
        enable_console=True,
        enable_events=False
    )
    
    try:
        test_position()
        test_account()
        test_risk_manager()
        test_simulated_broker()
        test_order_executor()
        test_portfolio_manager()
        
        print("=" * 50)
        print("✓ 所有交易执行与投资组合管理测试通过！")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
