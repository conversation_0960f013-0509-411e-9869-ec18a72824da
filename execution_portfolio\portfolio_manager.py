"""
投资组合管理器
负责资金管理、持仓跟踪、风险控制和绩效分析
"""

from __future__ import annotations
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from decimal import Decimal
import threading
import logging

from core.data_types import (
    PositionData, AccountData, OrderData, TradeData,
    Direction, OrderStatus, SymbolInfo
)
from core.event_types import (
    BaseEvent, SignalEvent, OrderEvent, TradeEvent,
    PositionUpdateEvent, AccountUpdateEvent, RiskWarningEvent
)


class Position:
    """持仓信息"""

    def __init__(self, symbol: str, direction: Direction):
        self.symbol = symbol
        self.direction = direction
        self.volume = 0
        self.frozen = 0  # 冻结数量
        self.avg_price = Decimal('0')
        self.market_value = Decimal('0')
        self.pnl = Decimal('0')
        self.realized_pnl = Decimal('0')
        self.unrealized_pnl = Decimal('0')
        self.last_update = datetime.now()

        # 交易记录
        self.trades: List[TradeData] = []

    def add_trade(self, trade: TradeData) -> None:
        """添加交易记录"""
        self.trades.append(trade)

        if trade.direction == self.direction:
            # 开仓或加仓
            if self.volume == 0:
                self.avg_price = trade.price
                self.volume = trade.volume
            else:
                total_value = self.avg_price * self.volume + trade.price * trade.volume
                total_volume = self.volume + trade.volume
                self.avg_price = total_value / total_volume
                self.volume = total_volume
        else:
            # 平仓
            close_volume = min(trade.volume, self.volume)
            if close_volume > 0:
                # 计算已实现盈亏
                if self.direction == Direction.LONG:
                    realized_pnl = (trade.price - self.avg_price) * close_volume
                else:
                    realized_pnl = (self.avg_price - trade.price) * close_volume

                self.realized_pnl += realized_pnl
                self.volume -= close_volume

                # 如果完全平仓，重置平均价格
                if self.volume == 0:
                    self.avg_price = Decimal('0')

        self.last_update = datetime.now()

    def update_market_price(self, market_price: Decimal) -> None:
        """更新市场价格和未实现盈亏"""
        if self.volume > 0:
            self.market_value = market_price * self.volume

            if self.direction == Direction.LONG:
                self.unrealized_pnl = (market_price - self.avg_price) * self.volume
            else:
                self.unrealized_pnl = (self.avg_price - market_price) * self.volume

            self.pnl = self.realized_pnl + self.unrealized_pnl
        else:
            self.market_value = Decimal('0')
            self.unrealized_pnl = Decimal('0')
            self.pnl = self.realized_pnl

    def to_position_data(self) -> PositionData:
        """转换为PositionData"""
        return PositionData(
            symbol=self.symbol,
            direction=self.direction,
            volume=self.volume,
            frozen=self.frozen,
            price=self.avg_price,
            pnl=self.pnl,
            datetime=self.last_update
        )


class Account:
    """账户信息"""

    def __init__(self, account_id: str, initial_balance: Decimal):
        self.account_id = account_id
        self.balance = initial_balance
        self.available = initial_balance
        self.frozen = Decimal('0')
        self.market_value = Decimal('0')
        self.total_pnl = Decimal('0')
        self.commission_paid = Decimal('0')
        self.last_update = datetime.now()

        # 历史记录
        self.balance_history: List[Tuple[datetime, Decimal]] = []
        self.balance_history.append((datetime.now(), initial_balance))

    def freeze_funds(self, amount: Decimal) -> bool:
        """冻结资金"""
        if self.available >= amount:
            self.available -= amount
            self.frozen += amount
            self.last_update = datetime.now()
            return True
        return False

    def unfreeze_funds(self, amount: Decimal) -> None:
        """解冻资金"""
        unfreeze_amount = min(amount, self.frozen)
        self.frozen -= unfreeze_amount
        self.available += unfreeze_amount
        self.last_update = datetime.now()

    def update_balance(self, amount: Decimal) -> None:
        """更新余额"""
        self.balance += amount
        self.available += amount
        self.last_update = datetime.now()

        # 记录历史
        self.balance_history.append((datetime.now(), self.balance))

        # 限制历史记录数量
        if len(self.balance_history) > 10000:
            self.balance_history = self.balance_history[-5000:]

    def update_market_value(self, market_value: Decimal, total_pnl: Decimal) -> None:
        """更新市值和盈亏"""
        self.market_value = market_value
        self.total_pnl = total_pnl
        self.last_update = datetime.now()

    def to_account_data(self) -> AccountData:
        """转换为AccountData"""
        return AccountData(
            account_id=self.account_id,
            balance=self.balance,
            frozen=self.frozen,
            available=self.available,
            datetime=self.last_update
        )


class RiskManager:
    """风险管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.RiskManager")

        # 风险参数
        self.max_position_ratio = config.get('max_position_size', 0.1)  # 最大单个持仓比例
        self.max_daily_loss_ratio = config.get('max_daily_loss', 0.05)  # 最大日损失比例
        self.max_drawdown_ratio = config.get('max_drawdown', 0.2)  # 最大回撤比例

        # 风险状态
        self.daily_start_balance = Decimal('0')
        self.peak_balance = Decimal('0')
        self.risk_warnings: List[str] = []

    def check_order_risk(self, order: OrderData, account: Account, positions: Dict[str, Position]) -> Tuple[bool, str]:
        """检查订单风险"""
        # 检查资金充足性
        if not self._check_funds(order, account):
            return False, "资金不足"

        # 检查持仓比例
        if not self._check_position_ratio(order, account):
            return False, f"持仓比例超过限制 {self.max_position_ratio:.1%}"

        return True, ""

    def check_account_risk(self, account: Account) -> List[str]:
        """检查账户风险"""
        warnings = []

        # 检查日损失
        if self.daily_start_balance > 0:
            daily_loss_ratio = (self.daily_start_balance - account.balance) / self.daily_start_balance
            if daily_loss_ratio > self.max_daily_loss_ratio:
                warnings.append(f"日损失超过限制: {daily_loss_ratio:.2%} > {self.max_daily_loss_ratio:.2%}")

        # 检查最大回撤
        if account.balance > self.peak_balance:
            self.peak_balance = account.balance

        if self.peak_balance > 0:
            drawdown_ratio = (self.peak_balance - account.balance) / self.peak_balance
            if drawdown_ratio > self.max_drawdown_ratio:
                warnings.append(f"回撤超过限制: {drawdown_ratio:.2%} > {self.max_drawdown_ratio:.2%}")

        return warnings

    def _check_funds(self, order: OrderData, account: Account) -> bool:
        """检查资金充足性"""
        # 简化计算，假设需要全额资金
        required_funds = order.price * order.volume
        return account.available >= required_funds

    def _check_position_ratio(self, order: OrderData, account: Account) -> bool:
        """检查持仓比例"""
        order_value = order.price * order.volume
        position_ratio = order_value / account.balance
        return position_ratio <= self.max_position_ratio

    def set_daily_start_balance(self, balance: Decimal) -> None:
        """设置日初余额"""
        self.daily_start_balance = balance


class PortfolioManager:
    """
    投资组合管理器

    特性：
    1. 实时持仓跟踪
    2. 资金管理
    3. 风险控制
    4. 绩效分析
    5. 事件驱动更新
    """

    def __init__(self, event_bus: Any, config: Any):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 核心组件
        self.account: Optional[Account] = None
        self.positions: Dict[str, Position] = {}  # symbol -> Position
        self.risk_manager: Optional[RiskManager] = None

        # 配置
        self.portfolio_config = config.get('execution', {})
        self.initial_capital = Decimal(str(self.portfolio_config.get('initial_capital', 1000000)))
        self.commission_rate = Decimal(str(self.portfolio_config.get('commission_rate', 0.0003)))

        # 运行状态
        self._running = False
        self._lock = threading.RLock()

        # 市场价格缓存
        self._market_prices: Dict[str, Decimal] = {}

        # 统计信息
        self.stats = {
            'total_trades': 0,
            'total_commission': Decimal('0'),
            'total_pnl': Decimal('0'),
            'max_drawdown': Decimal('0'),
            'start_time': None
        }

    def start(self) -> None:
        """启动投资组合管理器"""
        if self._running:
            self.logger.warning("投资组合管理器已在运行")
            return

        try:
            self.logger.info("启动投资组合管理器...")

            # 初始化账户
            self.account = Account("main", self.initial_capital)

            # 初始化风险管理器
            risk_config = self.config.get('risk_management', {})
            self.risk_manager = RiskManager(risk_config)
            self.risk_manager.set_daily_start_balance(self.initial_capital)

            # 订阅事件
            self._subscribe_events()

            self._running = True
            self.stats['start_time'] = datetime.now()

            self.logger.info("投资组合管理器启动成功")

        except Exception as e:
            self.logger.error(f"启动投资组合管理器失败: {e}")
            raise

    def stop(self) -> None:
        """停止投资组合管理器"""
        if not self._running:
            return

        try:
            self.logger.info("停止投资组合管理器...")
            self._running = False

            # 生成最终报告
            self._generate_final_report()

            self.logger.info("投资组合管理器已停止")

        except Exception as e:
            self.logger.error(f"停止投资组合管理器失败: {e}")

    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            # 订阅交易信号
            self.event_bus.subscribe("signal", self._on_signal_event, "portfolio_signal")

            # 订阅成交事件
            self.event_bus.subscribe("trade", self._on_trade_event, "portfolio_trade")

            # 订阅价格更新事件
            self.event_bus.subscribe("tick", self._on_price_update, "portfolio_price")
            self.event_bus.subscribe("bar", self._on_price_update, "portfolio_price")

    def _on_signal_event(self, event: SignalEvent) -> None:
        """处理交易信号事件"""
        try:
            with self._lock:
                # 这里可以添加信号过滤、风险检查等逻辑
                self.logger.info(f"收到交易信号: {event.symbol} {event.direction} {event.volume}")

                # 转发给订单执行器（通过事件总线）
                if self.event_bus:
                    self.event_bus.publish(event)

        except Exception as e:
            self.logger.error(f"处理交易信号失败: {e}")

    def _on_trade_event(self, event: TradeEvent) -> None:
        """处理成交事件"""
        try:
            with self._lock:
                trade = event.data
                self._process_trade(trade)

        except Exception as e:
            self.logger.error(f"处理成交事件失败: {e}")

    def _on_price_update(self, event) -> None:
        """处理价格更新事件"""
        try:
            with self._lock:
                if hasattr(event, 'data'):
                    if hasattr(event.data, 'symbol') and hasattr(event.data, 'last_price'):
                        # Tick数据
                        symbol = event.data.symbol
                        price = event.data.last_price
                    elif hasattr(event.data, 'symbol') and hasattr(event.data, 'close_price'):
                        # K线数据
                        symbol = event.data.symbol
                        price = event.data.close_price
                    else:
                        return

                    self._update_market_price(symbol, price)

        except Exception as e:
            self.logger.error(f"处理价格更新失败: {e}")

    def _process_trade(self, trade: TradeData) -> None:
        """处理成交"""
        # 更新持仓
        position_key = f"{trade.symbol}_{trade.direction.value}"

        if position_key not in self.positions:
            self.positions[position_key] = Position(trade.symbol, trade.direction)

        position = self.positions[position_key]
        position.add_trade(trade)

        # 更新账户
        if self.account:
            # 扣除手续费
            self.account.update_balance(-trade.commission)
            self.stats['total_commission'] += trade.commission

        # 更新统计
        self.stats['total_trades'] += 1

        # 发布持仓更新事件
        if self.event_bus:
            position_event = PositionUpdateEvent(
                data=position.to_position_data(),
                source="portfolio_manager"
            )
            self.event_bus.publish(position_event)

        self.logger.info(f"处理成交: {trade.symbol} {trade.direction.value} {trade.volume} @ {trade.price}")

    def _update_market_price(self, symbol: str, price: Decimal) -> None:
        """更新市场价格"""
        self._market_prices[symbol] = price

        # 更新相关持仓的市值和盈亏
        for position in self.positions.values():
            if position.symbol == symbol:
                position.update_market_price(price)

        # 更新账户市值
        self._update_account_market_value()

        # 风险检查
        self._check_risks()

    def _update_account_market_value(self) -> None:
        """更新账户市值"""
        if not self.account:
            return

        total_market_value = Decimal('0')
        total_pnl = Decimal('0')

        for position in self.positions.values():
            if position.volume > 0:
                total_market_value += position.market_value
                total_pnl += position.pnl

        self.account.update_market_value(total_market_value, total_pnl)
        self.stats['total_pnl'] = total_pnl

        # 发布账户更新事件
        if self.event_bus:
            account_event = AccountUpdateEvent(
                data=self.account.to_account_data(),
                source="portfolio_manager"
            )
            self.event_bus.publish(account_event)

    def _check_risks(self) -> None:
        """检查风险"""
        if not self.account or not self.risk_manager:
            return

        warnings = self.risk_manager.check_account_risk(self.account)

        for warning in warnings:
            self.logger.warning(f"风险警告: {warning}")

            if self.event_bus:
                risk_event = RiskWarningEvent(
                    risk_type="account_risk",
                    message=warning,
                    severity="WARNING",
                    source="portfolio_manager"
                )
                self.event_bus.publish(risk_event)

    def _generate_final_report(self) -> None:
        """生成最终报告"""
        if not self.account:
            return

        final_balance = self.account.balance + self.account.market_value
        total_return = (final_balance - self.initial_capital) / self.initial_capital

        self.logger.info("=== 投资组合最终报告 ===")
        self.logger.info(f"初始资金: {self.initial_capital}")
        self.logger.info(f"最终余额: {final_balance}")
        self.logger.info(f"总收益率: {total_return:.2%}")
        self.logger.info(f"总交易次数: {self.stats['total_trades']}")
        self.logger.info(f"总手续费: {self.stats['total_commission']}")
        self.logger.info(f"总盈亏: {self.stats['total_pnl']}")

    def get_account_info(self) -> Optional[AccountData]:
        """获取账户信息"""
        if self.account:
            return self.account.to_account_data()
        return None

    def get_positions(self) -> List[PositionData]:
        """获取所有持仓"""
        with self._lock:
            return [pos.to_position_data() for pos in self.positions.values() if pos.volume > 0]

    def get_position(self, symbol: str, direction: Direction) -> Optional[PositionData]:
        """获取指定持仓"""
        position_key = f"{symbol}_{direction.value}"
        position = self.positions.get(position_key)
        if position and position.volume > 0:
            return position.to_position_data()
        return None

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()

        if self.account:
            stats['current_balance'] = float(self.account.balance)
            stats['available_funds'] = float(self.account.available)
            stats['market_value'] = float(self.account.market_value)
            stats['total_value'] = float(self.account.balance + self.account.market_value)

            if self.initial_capital > 0:
                stats['total_return'] = float((self.account.balance + self.account.market_value - self.initial_capital) / self.initial_capital)

        stats['active_positions'] = len([pos for pos in self.positions.values() if pos.volume > 0])

        return stats
