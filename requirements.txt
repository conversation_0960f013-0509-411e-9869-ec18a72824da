# 量化交易系统依赖包
# 要求 Python 3.13+

# 核心数据处理
pandas>=2.1.0
numpy>=1.25.0
pyarrow>=14.0.0  # 用于 Feather/Parquet 格式

# GUI框架
PySide6>=6.6.0
QtAwesome>=1.3.0
pyqtgraph>=0.13.0

# 数据存储
# SQLite3  # Python 内置
PyYAML>=6.0.1

# 网络和API
requests>=2.31.0
websocket-client>=1.6.0
aiohttp>=3.9.0

# 数学和统计
scipy>=1.11.0
scikit-learn>=1.3.0
statsmodels>=0.14.0

# 技术分析
TA-Lib>=0.4.25  # 需要单独安装二进制包
pandas-ta>=0.3.14b

# AI优化
optuna>=3.4.0
hyperopt>=0.2.7

# 时间处理
python-dateutil>=2.8.2
pytz>=2023.3

# 配置和序列化
pydantic>=2.5.0
marshmallow>=3.20.0

# 类型支持
typing_extensions>=4.8.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 开发工具
black>=23.0.0
isort>=5.12.0
mypy>=1.7.0
flake8>=6.1.0

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 日志和监控
structlog>=23.2.0

# 数据可视化
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.17.0

# 加密货币数据源（可选）
ccxt>=4.1.0
python-binance>=1.0.28

# 股票数据源（可选）
yfinance>=0.2.0
akshare>=1.12.0

# 数据库（可选）
sqlalchemy>=2.0.0
redis>=5.0.0

# 消息队列（可选）
celery>=5.3.0

# 文档生成
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
