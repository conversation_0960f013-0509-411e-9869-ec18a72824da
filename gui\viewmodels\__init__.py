"""
GUI ViewModels 模块
实现MVVM架构中的ViewModel层
"""

from .chart_vm import ChartViewModel
from .portfolio_vm import PortfolioViewModel
from .trading_vm import TradingViewModel
from .log_vm import LogViewModel

# 可选的虚拟币ViewModel
try:
    from .crypto_list_vm import CryptoListViewModel
    CRYPTO_AVAILABLE = True
except ImportError:
    CryptoListViewModel = None
    CRYPTO_AVAILABLE = False

__all__ = [
    'ChartViewModel',
    'PortfolioViewModel',
    'TradingViewModel',
    'LogViewModel'
]

if CRYPTO_AVAILABLE:
    __all__.append('CryptoListViewModel')
