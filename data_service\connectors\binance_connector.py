"""
Binance数据连接器
使用python-binance库连接Binance API获取加密货币数据
"""

from __future__ import annotations
import asyncio
import threading
import time
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from decimal import Decimal
import logging

try:
    from binance import Client, ThreadedWebsocketManager
    from binance.exceptions import BinanceAPIException, BinanceRequestException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False

from .base_connector import BaseConnector
from core.data_types import TickData, BarData, SymbolInfo, Interval


class BinanceConnector(BaseConnector):
    """
    Binance数据连接器
    
    支持功能:
    1. 获取交易品种信息
    2. 获取历史K线数据
    3. 实时价格数据推送
    4. WebSocket数据流
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        
        if not BINANCE_AVAILABLE:
            raise ImportError("python-binance库未安装，请运行: pip install python-binance")
        
        # API配置
        self.api_key = config.get('api_key', '')
        self.api_secret = config.get('api_secret', '')
        self.testnet = config.get('testnet', False)
        self.base_url = config.get('base_url', '')
        
        # 客户端
        self.client: Optional[Client] = None
        self.ws_manager: Optional[ThreadedWebsocketManager] = None
        
        # 数据缓存
        self._symbol_info_cache: Dict[str, SymbolInfo] = {}
        self._latest_prices: Dict[str, Decimal] = {}
        self._ws_streams: Dict[str, str] = {}  # symbol -> stream_name
        
        # 线程控制
        self._ws_thread: Optional[threading.Thread] = None
        self._running = False
        
        # Binance时间间隔映射
        self.interval_mapping = {
            '1m': Client.KLINE_INTERVAL_1MINUTE,
            '3m': Client.KLINE_INTERVAL_3MINUTE,
            '5m': Client.KLINE_INTERVAL_5MINUTE,
            '15m': Client.KLINE_INTERVAL_15MINUTE,
            '30m': Client.KLINE_INTERVAL_30MINUTE,
            '1h': Client.KLINE_INTERVAL_1HOUR,
            '2h': Client.KLINE_INTERVAL_2HOUR,
            '4h': Client.KLINE_INTERVAL_4HOUR,
            '6h': Client.KLINE_INTERVAL_6HOUR,
            '8h': Client.KLINE_INTERVAL_8HOUR,
            '12h': Client.KLINE_INTERVAL_12HOUR,
            '1d': Client.KLINE_INTERVAL_1DAY,
            '3d': Client.KLINE_INTERVAL_3DAY,
            '1w': Client.KLINE_INTERVAL_1WEEK,
            '1M': Client.KLINE_INTERVAL_1MONTH
        }
    
    def connect(self) -> bool:
        """连接到Binance API"""
        try:
            # 创建客户端
            if self.api_key and self.api_secret:
                self.client = Client(
                    api_key=self.api_key,
                    api_secret=self.api_secret,
                    testnet=self.testnet
                )
                self.logger.info("使用API密钥连接Binance")
            else:
                self.client = Client(testnet=self.testnet)
                self.logger.info("使用公共API连接Binance")
            
            # 测试连接
            server_time = self.client.get_server_time()
            self.logger.info(f"Binance服务器时间: {server_time}")
            
            # 获取交易所信息
            exchange_info = self.client.get_exchange_info()
            self.logger.info(f"连接到Binance，支持{len(exchange_info['symbols'])}个交易对")
            
            # 初始化WebSocket管理器
            self._init_websocket_manager()
            
            self._on_connection_status(True, "成功连接到Binance API")
            return True
            
        except BinanceAPIException as e:
            self.logger.error(f"Binance API错误: {e}")
            self._on_connection_status(False, f"API错误: {e}")
            return False
        except BinanceRequestException as e:
            self.logger.error(f"Binance请求错误: {e}")
            self._on_connection_status(False, f"请求错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"连接Binance失败: {e}")
            self._on_connection_status(False, str(e))
            return False
    
    def disconnect(self) -> bool:
        """断开Binance连接"""
        try:
            self._running = False
            
            # 停止WebSocket管理器
            if self.ws_manager:
                self.ws_manager.stop()
                self.ws_manager = None
            
            # 等待线程结束
            if self._ws_thread and self._ws_thread.is_alive():
                self._ws_thread.join(timeout=5)
            
            # 清理缓存
            self._symbol_info_cache.clear()
            self._latest_prices.clear()
            self._ws_streams.clear()
            self._subscribed_symbols.clear()
            
            self.client = None
            self._on_connection_status(False, "已断开Binance连接")
            return True
            
        except Exception as e:
            self.logger.error(f"断开Binance连接失败: {e}")
            return False
    
    def subscribe(self, symbol: str) -> bool:
        """订阅品种数据"""
        try:
            if not self.client:
                self.logger.error("未连接到Binance")
                return False
            
            if symbol in self._subscribed_symbols:
                return True
            
            # 验证品种是否存在
            symbol_info = self.get_symbol_info(symbol)
            if not symbol_info:
                self.logger.error(f"品种不存在: {symbol}")
                return False
            
            # 启动WebSocket数据流
            if self.ws_manager and self._running:
                self._start_symbol_stream(symbol)
            
            self._subscribed_symbols.append(symbol)
            self.logger.info(f"成功订阅品种: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"订阅品种 {symbol} 失败: {e}")
            return False
    
    def unsubscribe(self, symbol: str) -> bool:
        """取消订阅品种"""
        try:
            if symbol in self._subscribed_symbols:
                self._subscribed_symbols.remove(symbol)
                
                # 停止WebSocket流
                if symbol in self._ws_streams and self.ws_manager:
                    stream_name = self._ws_streams[symbol]
                    # 注意：python-binance的ThreadedWebsocketManager没有直接的停止单个流的方法
                    # 这里我们只是从缓存中移除
                    del self._ws_streams[symbol]
                
                # 清理价格缓存
                if symbol in self._latest_prices:
                    del self._latest_prices[symbol]
                
                self.logger.info(f"取消订阅品种: {symbol}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"取消订阅品种 {symbol} 失败: {e}")
            return False
    
    def get_symbol_info(self, symbol: str) -> Optional[SymbolInfo]:
        """获取品种信息"""
        try:
            if symbol in self._symbol_info_cache:
                return self._symbol_info_cache[symbol]
            
            if not self.client:
                return None
            
            # 获取交易所信息
            exchange_info = self.client.get_exchange_info()
            
            # 查找指定品种
            symbol_data = None
            for s in exchange_info['symbols']:
                if s['symbol'] == symbol.upper():
                    symbol_data = s
                    break
            
            if not symbol_data:
                return None
            
            # 解析过滤器信息
            price_filter = next((f for f in symbol_data['filters'] if f['filterType'] == 'PRICE_FILTER'), {})
            lot_size_filter = next((f for f in symbol_data['filters'] if f['filterType'] == 'LOT_SIZE'), {})
            
            symbol_info: SymbolInfo = {
                'symbol': symbol_data['symbol'],
                'name': f"{symbol_data['baseAsset']}/{symbol_data['quoteAsset']}",
                'exchange': 'Binance',
                'product_type': 'crypto',
                'size': Decimal('1'),
                'price_tick': Decimal(price_filter.get('tickSize', '0.00000001')),
                'min_volume': int(float(lot_size_filter.get('minQty', '1'))),
                'margin_rate': Decimal('0.1')
            }
            
            self._symbol_info_cache[symbol] = symbol_info
            return symbol_info
            
        except Exception as e:
            self.logger.error(f"获取品种信息失败 {symbol}: {e}")
            return None
    
    def get_historical_bars(
        self, 
        symbol: str, 
        start_time: datetime, 
        end_time: datetime,
        interval: str = "1m"
    ) -> List[BarData]:
        """获取历史K线数据"""
        try:
            if not self.client:
                self.logger.error("未连接到Binance")
                return []
            
            # 转换时间间隔
            binance_interval = self.interval_mapping.get(interval)
            if not binance_interval:
                self.logger.error(f"不支持的时间间隔: {interval}")
                return []
            
            # 获取K线数据
            klines = self.client.get_historical_klines(
                symbol=symbol.upper(),
                interval=binance_interval,
                start_str=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_str=end_time.strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # 转换为BarData对象
            bars = []
            for kline in klines:
                bar = BarData(
                    symbol=symbol,
                    datetime=datetime.fromtimestamp(kline[0] / 1000),
                    interval=Interval(interval),
                    open_price=Decimal(kline[1]),
                    high_price=Decimal(kline[2]),
                    low_price=Decimal(kline[3]),
                    close_price=Decimal(kline[4]),
                    volume=int(float(kline[5])),
                    turnover=Decimal(kline[7])  # Quote asset volume
                )
                bars.append(bar)
            
            self.logger.info(f"获取历史数据: {symbol}, {len(bars)}条K线")
            return bars
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []
    
    def _init_websocket_manager(self) -> None:
        """初始化WebSocket管理器"""
        try:
            if self.api_key and self.api_secret:
                self.ws_manager = ThreadedWebsocketManager(
                    api_key=self.api_key,
                    api_secret=self.api_secret,
                    testnet=self.testnet
                )
            else:
                self.ws_manager = ThreadedWebsocketManager(testnet=self.testnet)
            
            self.ws_manager.start()
            self._running = True
            self.logger.info("WebSocket管理器已启动")
            
        except Exception as e:
            self.logger.error(f"初始化WebSocket管理器失败: {e}")
    
    def _start_symbol_stream(self, symbol: str) -> None:
        """启动品种的WebSocket数据流"""
        try:
            if not self.ws_manager:
                return
            
            # 启动ticker流（24小时价格统计）
            stream_name = self.ws_manager.start_symbol_ticker_socket(
                callback=self._handle_ticker_message,
                symbol=symbol.upper()
            )
            
            self._ws_streams[symbol] = stream_name
            self.logger.info(f"启动WebSocket流: {symbol}")
            
        except Exception as e:
            self.logger.error(f"启动WebSocket流失败 {symbol}: {e}")
    
    def _handle_ticker_message(self, msg: Dict[str, Any]) -> None:
        """处理ticker消息"""
        try:
            if msg.get('e') != '24hrTicker':
                return
            
            symbol = msg['s']
            price = Decimal(msg['c'])  # 当前价格
            volume = int(float(msg['v']))  # 24小时成交量
            
            # 更新价格缓存
            self._latest_prices[symbol] = price
            
            # 创建TickData
            tick = TickData(
                symbol=symbol,
                datetime=datetime.fromtimestamp(msg['E'] / 1000),
                last_price=price,
                volume=volume,
                turnover=Decimal(msg['q']),  # 24小时成交额
                bid_price_1=Decimal(msg['b']),  # 最佳买价
                ask_price_1=Decimal(msg['a'])   # 最佳卖价
            )
            
            # 发送事件
            self._on_tick_data(tick)
            
        except Exception as e:
            self.logger.error(f"处理ticker消息失败: {e}")
    
    def get_all_symbols(self) -> List[Dict[str, Any]]:
        """获取所有交易品种信息"""
        try:
            if not self.client:
                return []
            
            exchange_info = self.client.get_exchange_info()
            symbols = []
            
            for symbol_data in exchange_info['symbols']:
                if symbol_data['status'] == 'TRADING':
                    symbols.append({
                        'symbol': symbol_data['symbol'],
                        'baseAsset': symbol_data['baseAsset'],
                        'quoteAsset': symbol_data['quoteAsset'],
                        'status': symbol_data['status']
                    })
            
            return symbols
            
        except Exception as e:
            self.logger.error(f"获取所有品种失败: {e}")
            return []
    
    def get_24hr_ticker(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取24小时价格统计"""
        try:
            if not self.client:
                return []
            
            if symbol:
                ticker = self.client.get_ticker(symbol=symbol.upper())
                return [ticker] if ticker else []
            else:
                return self.client.get_ticker()
                
        except Exception as e:
            self.logger.error(f"获取24小时统计失败: {e}")
            return []
