"""
交易视图
提供手动交易操作界面
"""

from __future__ import annotations
from typing import Any, Optional
import logging
from decimal import Decimal

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QDoubleSpinBox, QGroupBox, QCheckBox, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from core.data_types import Direction, OrderType
from gui.viewmodels.trading_vm import TradingViewModel


class TradingControlPanel(QGroupBox):
    """交易控制面板"""
    
    # 信号定义
    buy_market_clicked = Signal(str, int)  # symbol, volume
    buy_limit_clicked = Signal(str, int, float)  # symbol, volume, price
    sell_market_clicked = Signal(str, int)  # symbol, volume
    sell_limit_clicked = Signal(str, int, float)  # symbol, volume, price
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("交易面板", parent)
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QGridLayout(self)
        
        # 品种输入
        layout.addWidget(QLabel("品种:"), 0, 0)
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setText("AAPL")
        self.symbol_edit.setPlaceholderText("输入品种代码")
        layout.addWidget(self.symbol_edit, 0, 1, 1, 2)
        
        # 数量输入
        layout.addWidget(QLabel("数量:"), 1, 0)
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(1, 999999)
        self.volume_spin.setValue(100)
        layout.addWidget(self.volume_spin, 1, 1, 1, 2)
        
        # 价格输入
        layout.addWidget(QLabel("价格:"), 2, 0)
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 999999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.setValue(100.00)
        layout.addWidget(self.price_spin, 2, 1, 1, 2)
        
        # 买入按钮
        self.buy_market_btn = QPushButton("市价买入")
        self.buy_market_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.buy_market_btn.clicked.connect(self._on_buy_market)
        layout.addWidget(self.buy_market_btn, 3, 0)
        
        self.buy_limit_btn = QPushButton("限价买入")
        self.buy_limit_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.buy_limit_btn.clicked.connect(self._on_buy_limit)
        layout.addWidget(self.buy_limit_btn, 3, 1)
        
        # 卖出按钮
        self.sell_market_btn = QPushButton("市价卖出")
        self.sell_market_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.sell_market_btn.clicked.connect(self._on_sell_market)
        layout.addWidget(self.sell_market_btn, 4, 0)
        
        self.sell_limit_btn = QPushButton("限价卖出")
        self.sell_limit_btn.setStyleSheet("background-color: #FF9800; color: white;")
        self.sell_limit_btn.clicked.connect(self._on_sell_limit)
        layout.addWidget(self.sell_limit_btn, 4, 1)
        
        # 平仓按钮
        self.close_all_btn = QPushButton("全部平仓")
        self.close_all_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        layout.addWidget(self.close_all_btn, 5, 0, 1, 2)
        
    def _on_buy_market(self) -> None:
        """市价买入"""
        symbol = self.symbol_edit.text().strip()
        volume = self.volume_spin.value()
        if symbol:
            self.buy_market_clicked.emit(symbol, volume)
            
    def _on_buy_limit(self) -> None:
        """限价买入"""
        symbol = self.symbol_edit.text().strip()
        volume = self.volume_spin.value()
        price = self.price_spin.value()
        if symbol:
            self.buy_limit_clicked.emit(symbol, volume, price)
            
    def _on_sell_market(self) -> None:
        """市价卖出"""
        symbol = self.symbol_edit.text().strip()
        volume = self.volume_spin.value()
        if symbol:
            self.sell_market_clicked.emit(symbol, volume)
            
    def _on_sell_limit(self) -> None:
        """限价卖出"""
        symbol = self.symbol_edit.text().strip()
        volume = self.volume_spin.value()
        price = self.price_spin.value()
        if symbol:
            self.sell_limit_clicked.emit(symbol, volume, price)
            
    def set_symbol(self, symbol: str) -> None:
        """设置品种"""
        self.symbol_edit.setText(symbol)
        
    def set_price(self, price: float) -> None:
        """设置价格"""
        self.price_spin.setValue(price)


class TradingStatusPanel(QGroupBox):
    """交易状态面板"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("交易状态", parent)
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 交易开关
        self.trading_enabled_cb = QCheckBox("启用交易")
        self.trading_enabled_cb.setChecked(True)
        layout.addWidget(self.trading_enabled_cb)
        
        # 自动交易开关
        self.auto_trading_cb = QCheckBox("自动交易")
        self.auto_trading_cb.setChecked(False)
        layout.addWidget(self.auto_trading_cb)
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 最后操作
        self.last_action_label = QLabel("最后操作: 无")
        layout.addWidget(self.last_action_label)
        
    def update_status(self, status: str, color: str = "black") -> None:
        """更新状态"""
        self.status_label.setText(f"状态: {status}")
        self.status_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        
    def update_last_action(self, action: str) -> None:
        """更新最后操作"""
        self.last_action_label.setText(f"最后操作: {action}")


class QuickTradePanel(QGroupBox):
    """快速交易面板"""
    
    # 信号定义
    quick_trade_clicked = Signal(str, str, int)  # symbol, action, volume
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("快速交易", parent)
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QGridLayout(self)
        
        # 快速数量按钮
        volumes = [100, 500, 1000, 5000]
        for i, volume in enumerate(volumes):
            row = i // 2
            col = i % 2
            
            buy_btn = QPushButton(f"买{volume}")
            buy_btn.setStyleSheet("background-color: #4CAF50; color: white;")
            buy_btn.clicked.connect(lambda checked, v=volume: self._on_quick_buy(v))
            layout.addWidget(buy_btn, row * 2, col)
            
            sell_btn = QPushButton(f"卖{volume}")
            sell_btn.setStyleSheet("background-color: #f44336; color: white;")
            sell_btn.clicked.connect(lambda checked, v=volume: self._on_quick_sell(v))
            layout.addWidget(sell_btn, row * 2 + 1, col)
            
    def _on_quick_buy(self, volume: int) -> None:
        """快速买入"""
        self.quick_trade_clicked.emit("", "buy", volume)
        
    def _on_quick_sell(self, volume: int) -> None:
        """快速卖出"""
        self.quick_trade_clicked.emit("", "sell", volume)


class TradingView(QWidget):
    """
    交易视图
    
    功能:
    1. 手动交易操作
    2. 交易状态控制
    3. 快速交易
    4. 交易参数设置
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[TradingViewModel] = None
        
        # UI组件
        self.control_panel: Optional[TradingControlPanel] = None
        self.status_panel: Optional[TradingStatusPanel] = None
        self.quick_panel: Optional[QuickTradePanel] = None
        
        # 初始化UI
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 交易控制面板
        self.control_panel = TradingControlPanel()
        layout.addWidget(self.control_panel)
        
        # 状态面板
        self.status_panel = TradingStatusPanel()
        layout.addWidget(self.status_panel)
        
        # 快速交易面板
        self.quick_panel = QuickTradePanel()
        layout.addWidget(self.quick_panel)
        
        # 连接信号
        self._connect_signals()
        
        layout.addStretch()
        
    def _connect_signals(self) -> None:
        """连接信号"""
        # 交易控制面板信号
        self.control_panel.buy_market_clicked.connect(self._on_buy_market)
        self.control_panel.buy_limit_clicked.connect(self._on_buy_limit)
        self.control_panel.sell_market_clicked.connect(self._on_sell_market)
        self.control_panel.sell_limit_clicked.connect(self._on_sell_limit)
        
        # 状态面板信号
        self.status_panel.trading_enabled_cb.toggled.connect(self._on_trading_enabled_changed)
        self.status_panel.auto_trading_cb.toggled.connect(self._on_auto_trading_changed)
        
        # 快速交易信号
        self.quick_panel.quick_trade_clicked.connect(self._on_quick_trade)
        
    def set_view_model(self, view_model: TradingViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接ViewModel信号
        self.view_model.order_submitted.connect(self._on_order_submitted)
        self.view_model.order_error.connect(self._on_order_error)
        self.view_model.trading_status_changed.connect(self._on_trading_status_changed)
        
        self.logger.info("交易ViewModel已连接")
        
    def _on_buy_market(self, symbol: str, volume: int) -> None:
        """市价买入处理"""
        if self.view_model:
            success = self.view_model.buy_market(symbol, volume)
            if success:
                self.status_panel.update_last_action(f"市价买入 {symbol} {volume}")
                
    def _on_buy_limit(self, symbol: str, volume: int, price: float) -> None:
        """限价买入处理"""
        if self.view_model:
            success = self.view_model.buy_limit(symbol, volume, Decimal(str(price)))
            if success:
                self.status_panel.update_last_action(f"限价买入 {symbol} {volume}@{price}")
                
    def _on_sell_market(self, symbol: str, volume: int) -> None:
        """市价卖出处理"""
        if self.view_model:
            success = self.view_model.sell_market(symbol, volume)
            if success:
                self.status_panel.update_last_action(f"市价卖出 {symbol} {volume}")
                
    def _on_sell_limit(self, symbol: str, volume: int, price: float) -> None:
        """限价卖出处理"""
        if self.view_model:
            success = self.view_model.sell_limit(symbol, volume, Decimal(str(price)))
            if success:
                self.status_panel.update_last_action(f"限价卖出 {symbol} {volume}@{price}")
                
    def _on_quick_trade(self, symbol: str, action: str, volume: int) -> None:
        """快速交易处理"""
        # 使用当前控制面板的品种
        current_symbol = self.control_panel.symbol_edit.text().strip()
        if not current_symbol:
            self.status_panel.update_status("请先设置品种", "red")
            return
            
        if self.view_model:
            if action == "buy":
                self.view_model.buy_market(current_symbol, volume)
            elif action == "sell":
                self.view_model.sell_market(current_symbol, volume)
                
    def _on_trading_enabled_changed(self, enabled: bool) -> None:
        """交易启用状态变化"""
        if self.view_model:
            self.view_model.set_trading_enabled(enabled)
            
    def _on_auto_trading_changed(self, enabled: bool) -> None:
        """自动交易状态变化"""
        if self.view_model:
            self.view_model.set_auto_trading(enabled)
            
    def _on_order_submitted(self, order_id: str) -> None:
        """订单提交成功"""
        self.status_panel.update_status("订单已提交", "green")
        
    def _on_order_error(self, error_msg: str) -> None:
        """订单错误"""
        self.status_panel.update_status(f"错误: {error_msg}", "red")
        
    def _on_trading_status_changed(self, enabled: bool) -> None:
        """交易状态变化"""
        status = "启用" if enabled else "禁用"
        color = "green" if enabled else "red"
        self.status_panel.update_status(f"交易{status}", color)
        
    def set_symbol(self, symbol: str) -> None:
        """设置品种"""
        self.control_panel.set_symbol(symbol)
        
    def set_price(self, price: float) -> None:
        """设置价格"""
        self.control_panel.set_price(price)
