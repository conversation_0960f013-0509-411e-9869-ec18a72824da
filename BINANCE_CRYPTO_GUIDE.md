# Binance虚拟币功能使用指南

## 概述

本项目已成功集成Binance数据源，为Gemini Quant量化交易系统添加了完整的虚拟币交易功能。用户可以通过GUI界面查看虚拟币列表、实时价格数据，并点击任意品种查看K线图。

## 功能特性

### ✅ 已实现功能

1. **Binance数据连接器**
   - 支持REST API和WebSocket连接
   - 获取所有交易品种信息
   - 获取24小时价格统计数据
   - 获取历史K线数据
   - 实时价格数据推送

2. **虚拟币列表界面**
   - 显示USDT交易对列表
   - 实时价格和24小时涨跌幅
   - 搜索和排序功能
   - 成交量等详细信息

3. **GUI集成**
   - 在主窗口菜单栏添加"虚拟币"菜单
   - 独立的虚拟币列表窗口
   - 点击品种自动切换K线图显示

4. **配置管理**
   - 支持API密钥配置（可选）
   - 测试网络支持
   - 灵活的连接参数配置

## 安装和配置

### 1. 安装依赖

确保已激活UV虚拟环境，然后安装crypto相关依赖：

```bash
# 激活UV虚拟环境
uv venv
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 安装crypto依赖（包含python-binance）
uv pip install -e ".[crypto]"
```

### 2. 配置Binance连接器

编辑 `config/default_config.yaml` 文件：

```yaml
data_service:
  connectors:
    binance:
      enabled: true
      api_key: ""        # 可选：Binance API密钥
      api_secret: ""     # 可选：Binance API密钥
      testnet: false     # 是否使用测试网
      base_url: ""       # 可选：自定义API地址
```

**注意：** 
- 如果不提供API密钥，将使用公共API（功能有限但足够查看价格数据）
- 提供API密钥可以获得更高的请求限制和更多功能

### 3. 获取Binance API密钥（可选）

1. 访问 [Binance API管理页面](https://www.binance.com/en/my/settings/api-management)
2. 创建新的API密钥
3. 设置适当的权限（只需要"读取"权限即可）
4. 将API密钥和密钥填入配置文件

## 使用方法

### 1. 启动系统

```bash
# 确保在UV虚拟环境中
python main.py
```

### 2. 访问虚拟币功能

1. 系统启动后，主窗口会显示
2. 在菜单栏中找到"虚拟币"菜单（位于"工具"和"帮助"之间）
3. 点击"虚拟币列表"打开虚拟币界面

### 3. 使用虚拟币列表

1. **查看数据**：列表显示所有USDT交易对的实时数据
2. **搜索品种**：在搜索框中输入品种名称进行过滤
3. **排序数据**：使用排序下拉框按不同条件排序
4. **查看K线**：双击任意品种行，主窗口的图表会切换到该品种的K线图
5. **刷新数据**：点击"刷新"按钮手动更新数据

### 4. 数据说明

列表显示的数据包括：
- **品种**：交易对符号（如BTCUSDT）
- **名称**：基础资产名称（如BTC）
- **价格(USDT)**：当前价格
- **24h涨跌**：24小时价格变化
- **24h涨跌%**：24小时涨跌百分比
- **24h成交量**：24小时成交量
- **24h最高/最低**：24小时最高价和最低价

## 测试功能

### 1. 测试Binance连接器

```bash
python test_binance_connector.py
```

这个测试会验证：
- Binance API连接
- 获取交易品种列表
- 获取品种信息
- 获取24小时价格统计
- 获取历史K线数据
- 订阅功能

### 2. 测试GUI功能

```bash
python test_crypto_gui.py
```

这个测试会打开虚拟币GUI界面，可以测试：
- 数据加载和显示
- 搜索和排序功能
- 品种选择功能

## 技术架构

### 1. 数据连接器层
- `BinanceConnector`: 实现与Binance API的通信
- 继承自`BaseConnector`，遵循统一的数据接口规范
- 支持REST API和WebSocket数据流

### 2. ViewModel层
- `CryptoListViewModel`: 管理虚拟币数据的业务逻辑
- 使用工作线程异步获取数据，避免阻塞UI
- 提供数据缓存和定时更新机制

### 3. View层
- `CryptoListView`: 虚拟币列表的UI界面
- 支持搜索、排序、选择等交互功能
- 使用Qt表格控件显示数据

### 4. 主窗口集成
- 在主窗口菜单中添加虚拟币入口
- 实现品种选择与图表视图的联动
- 独立窗口管理，不影响主界面布局

## 故障排除

### 1. 连接问题

**问题**：无法连接到Binance
**解决方案**：
- 检查网络连接
- 确认API密钥正确（如果使用）
- 检查防火墙设置
- 尝试使用测试网络（testnet: true）

### 2. 数据加载慢

**问题**：虚拟币列表加载缓慢
**解决方案**：
- 检查网络速度
- 减少显示的品种数量
- 使用API密钥提高请求限制

### 3. WebSocket错误

**问题**：实时数据推送失败
**解决方案**：
- WebSocket功能是可选的，不影响基本功能
- 检查网络稳定性
- 重启应用程序

### 4. GUI显示问题

**问题**：虚拟币窗口无法显示
**解决方案**：
- 确保PySide6已正确安装
- 检查UV虚拟环境是否激活
- 查看控制台错误信息

## 扩展功能

### 1. 添加更多交易所

可以参考`BinanceConnector`的实现，为其他交易所创建连接器：
- 继承`BaseConnector`基类
- 实现必需的抽象方法
- 在配置文件中添加相应配置

### 2. 增强数据分析

- 添加技术指标计算
- 实现价格预警功能
- 集成更多图表类型

### 3. 交易功能

- 实现模拟交易
- 添加订单管理
- 集成风险控制

## 注意事项

1. **API限制**：Binance对API请求有频率限制，请合理使用
2. **数据准确性**：价格数据仅供参考，实际交易请以交易所为准
3. **安全性**：API密钥请妥善保管，不要泄露给他人
4. **合规性**：请遵守当地法律法规进行虚拟币交易

## 支持

如果遇到问题或需要帮助，请：
1. 查看日志文件：`logs/quant_trader.log`
2. 运行测试脚本确认功能状态
3. 检查配置文件设置
4. 参考Binance API官方文档

---

**版本信息**：
- Gemini Quant v1.0
- python-binance v1.0.28+
- 支持Python 3.13+
