"""
数据处理器
实现K线合成、数据清洗等功能
"""

from __future__ import annotations
from collections import defaultdict, deque
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from decimal import Decimal
import logging

from core.data_types import TickData, BarData, Interval
from core.event_types import BaseEvent, TickEvent, BarEvent


class BarGenerator:
    """
    K线合成器
    
    将Tick数据合成为不同周期的K线数据
    """
    
    def __init__(self, symbol: str, interval: Interval, callback: Optional[Callable[[BarData], None]] = None):
        self.symbol = symbol
        self.interval = interval
        self.callback = callback
        self.logger = logging.getLogger(f"{__name__}.{symbol}.{interval.value}")
        
        # 当前K线数据
        self.current_bar: Optional[BarData] = None
        self.bar_start_time: Optional[datetime] = None
        
        # 统计信息
        self.tick_count = 0
        self.bar_count = 0
    
    def update_tick(self, tick: TickData) -> Optional[BarData]:
        """
        更新Tick数据，可能生成新的K线
        
        Args:
            tick: Tick数据
            
        Returns:
            BarData: 如果生成了新K线则返回，否则返回None
        """
        self.tick_count += 1
        
        # 计算当前Tick应该属于哪个K线周期
        bar_time = self._get_bar_time(tick.datetime)
        
        # 如果是新的K线周期，先完成当前K线
        completed_bar = None
        if self.bar_start_time is None or bar_time != self.bar_start_time:
            if self.current_bar is not None:
                completed_bar = self.current_bar
                self.bar_count += 1
                self.logger.debug(f"完成K线: {completed_bar.datetime}")
                
                # 调用回调函数
                if self.callback:
                    self.callback(completed_bar)
            
            # 开始新的K线
            self._start_new_bar(tick, bar_time)
        else:
            # 更新当前K线
            self._update_current_bar(tick)
        
        return completed_bar
    
    def get_current_bar(self) -> Optional[BarData]:
        """获取当前未完成的K线"""
        return self.current_bar
    
    def force_complete_bar(self) -> Optional[BarData]:
        """强制完成当前K线"""
        if self.current_bar is not None:
            completed_bar = self.current_bar
            self.current_bar = None
            self.bar_start_time = None
            self.bar_count += 1
            
            if self.callback:
                self.callback(completed_bar)
            
            return completed_bar
        return None
    
    def _get_bar_time(self, tick_time: datetime) -> datetime:
        """计算Tick时间对应的K线开始时间"""
        if self.interval == Interval.MINUTE_1:
            return tick_time.replace(second=0, microsecond=0)
        elif self.interval == Interval.MINUTE_5:
            minute = (tick_time.minute // 5) * 5
            return tick_time.replace(minute=minute, second=0, microsecond=0)
        elif self.interval == Interval.MINUTE_15:
            minute = (tick_time.minute // 15) * 15
            return tick_time.replace(minute=minute, second=0, microsecond=0)
        elif self.interval == Interval.MINUTE_30:
            minute = (tick_time.minute // 30) * 30
            return tick_time.replace(minute=minute, second=0, microsecond=0)
        elif self.interval == Interval.HOUR_1:
            return tick_time.replace(minute=0, second=0, microsecond=0)
        elif self.interval == Interval.HOUR_4:
            hour = (tick_time.hour // 4) * 4
            return tick_time.replace(hour=hour, minute=0, second=0, microsecond=0)
        elif self.interval == Interval.DAY_1:
            return tick_time.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # 默认返回分钟级别
            return tick_time.replace(second=0, microsecond=0)
    
    def _start_new_bar(self, tick: TickData, bar_time: datetime) -> None:
        """开始新的K线"""
        self.current_bar = BarData(
            symbol=self.symbol,
            datetime=bar_time,
            interval=self.interval,
            open_price=tick.last_price,
            high_price=tick.last_price,
            low_price=tick.last_price,
            close_price=tick.last_price,
            volume=tick.volume,
            turnover=tick.turnover
        )
        self.bar_start_time = bar_time
        self.logger.debug(f"开始新K线: {bar_time}")
    
    def _update_current_bar(self, tick: TickData) -> None:
        """更新当前K线"""
        if self.current_bar is None:
            return
        
        # 更新价格
        if tick.last_price > self.current_bar.high_price:
            self.current_bar = BarData(
                symbol=self.current_bar.symbol,
                datetime=self.current_bar.datetime,
                interval=self.current_bar.interval,
                open_price=self.current_bar.open_price,
                high_price=tick.last_price,
                low_price=self.current_bar.low_price,
                close_price=tick.last_price,
                volume=self.current_bar.volume + tick.volume,
                turnover=self.current_bar.turnover + tick.turnover
            )
        elif tick.last_price < self.current_bar.low_price:
            self.current_bar = BarData(
                symbol=self.current_bar.symbol,
                datetime=self.current_bar.datetime,
                interval=self.current_bar.interval,
                open_price=self.current_bar.open_price,
                high_price=self.current_bar.high_price,
                low_price=tick.last_price,
                close_price=tick.last_price,
                volume=self.current_bar.volume + tick.volume,
                turnover=self.current_bar.turnover + tick.turnover
            )
        else:
            self.current_bar = BarData(
                symbol=self.current_bar.symbol,
                datetime=self.current_bar.datetime,
                interval=self.current_bar.interval,
                open_price=self.current_bar.open_price,
                high_price=self.current_bar.high_price,
                low_price=self.current_bar.low_price,
                close_price=tick.last_price,
                volume=self.current_bar.volume + tick.volume,
                turnover=self.current_bar.turnover + tick.turnover
            )


class DataProcessor:
    """
    数据处理器
    
    负责数据清洗、K线合成、数据转换等功能
    """
    
    def __init__(self, event_callback: Optional[Callable[[BaseEvent], None]] = None):
        self.event_callback = event_callback
        self.logger = logging.getLogger(__name__)
        
        # K线合成器管理
        self.bar_generators: Dict[str, BarGenerator] = {}
        
        # 数据清洗配置
        self.enable_data_cleaning = True
        self.max_price_change_ratio = 0.1  # 最大价格变化比例
        self.min_volume = 0  # 最小成交量
        
        # 统计信息
        self.processed_ticks = 0
        self.generated_bars = 0
        self.cleaned_ticks = 0
    
    def process_tick(self, tick: TickData) -> List[BarData]:
        """
        处理Tick数据
        
        Args:
            tick: Tick数据
            
        Returns:
            List[BarData]: 生成的K线数据列表
        """
        self.processed_ticks += 1
        
        # 数据清洗
        if self.enable_data_cleaning and not self._is_valid_tick(tick):
            self.cleaned_ticks += 1
            self.logger.debug(f"清洗无效Tick数据: {tick}")
            return []
        
        # K线合成
        generated_bars = []
        
        # 为每个已注册的K线周期生成K线
        for generator_key, generator in self.bar_generators.items():
            if generator.symbol == tick.symbol:
                completed_bar = generator.update_tick(tick)
                if completed_bar:
                    generated_bars.append(completed_bar)
                    self.generated_bars += 1
                    
                    # 发布K线事件
                    if self.event_callback:
                        bar_event = BarEvent(data=completed_bar)
                        self.event_callback(bar_event)
        
        return generated_bars
    
    def register_bar_generator(self, symbol: str, interval: Interval) -> str:
        """
        注册K线合成器
        
        Args:
            symbol: 品种代码
            interval: 时间周期
            
        Returns:
            str: 合成器ID
        """
        generator_key = f"{symbol}_{interval.value}"
        
        if generator_key not in self.bar_generators:
            generator = BarGenerator(
                symbol=symbol,
                interval=interval,
                callback=self._on_bar_generated
            )
            self.bar_generators[generator_key] = generator
            self.logger.info(f"注册K线合成器: {generator_key}")
        
        return generator_key
    
    def unregister_bar_generator(self, generator_key: str) -> bool:
        """取消注册K线合成器"""
        if generator_key in self.bar_generators:
            # 强制完成当前K线
            generator = self.bar_generators[generator_key]
            generator.force_complete_bar()
            
            del self.bar_generators[generator_key]
            self.logger.info(f"取消注册K线合成器: {generator_key}")
            return True
        return False
    
    def get_current_bars(self) -> Dict[str, BarData]:
        """获取所有当前未完成的K线"""
        current_bars = {}
        for generator_key, generator in self.bar_generators.items():
            current_bar = generator.get_current_bar()
            if current_bar:
                current_bars[generator_key] = current_bar
        return current_bars
    
    def force_complete_all_bars(self) -> List[BarData]:
        """强制完成所有当前K线"""
        completed_bars = []
        for generator in self.bar_generators.values():
            completed_bar = generator.force_complete_bar()
            if completed_bar:
                completed_bars.append(completed_bar)
        return completed_bars
    
    def _is_valid_tick(self, tick: TickData) -> bool:
        """验证Tick数据是否有效"""
        # 检查价格是否为正数
        if tick.last_price <= 0:
            return False
        
        # 检查成交量是否满足最小要求
        if tick.volume < self.min_volume:
            return False
        
        # 可以添加更多验证逻辑
        # 例如：价格跳跃检查、时间序列检查等
        
        return True
    
    def _on_bar_generated(self, bar: BarData) -> None:
        """K线生成回调"""
        self.logger.debug(f"生成K线: {bar.symbol} {bar.interval.value} {bar.datetime}")
    
    def get_statistics(self) -> Dict[str, int]:
        """获取处理统计信息"""
        return {
            'processed_ticks': self.processed_ticks,
            'generated_bars': self.generated_bars,
            'cleaned_ticks': self.cleaned_ticks,
            'active_generators': len(self.bar_generators)
        }
