"""
事件类型定义
简化版本，确保兼容性
"""

from __future__ import annotations
from abc import ABC
from datetime import datetime
from typing import Any, Dict, Optional, Type, ClassVar
from uuid import uuid4

from .data_types import TickData, BarData, OrderData, TradeData, PositionData, AccountData


class BaseEvent(ABC):
    """事件基类"""
    EVENT_TYPE: ClassVar[str] = "base"

    def __init__(self, event_id: str = None, timestamp: datetime = None, source: str = "system"):
        self.event_id = event_id or str(uuid4())
        self.timestamp = timestamp or datetime.now()
        self.source = source

    @classmethod
    def get_event_type(cls) -> str:
        """获取事件类型"""
        return cls.EVENT_TYPE


# 数据事件
class TickEvent(BaseEvent):
    """Tick数据事件"""
    EVENT_TYPE: ClassVar[str] = "tick"

    def __init__(self, data: TickData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class BarEvent(BaseEvent):
    """K线数据事件"""
    EVENT_TYPE: ClassVar[str] = "bar"

    def __init__(self, data: BarData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


# 交易事件
class SignalEvent(BaseEvent):
    """交易信号事件"""
    EVENT_TYPE: ClassVar[str] = "signal"

    def __init__(self, symbol: str, direction: str, volume: int,
                 price: Optional[float] = None, strategy_name: str = "unknown",
                 signal_strength: float = 1.0, **kwargs):
        super().__init__(**kwargs)
        self.symbol = symbol
        self.direction = direction
        self.volume = volume
        self.price = price
        self.strategy_name = strategy_name
        self.signal_strength = signal_strength


class OrderEvent(BaseEvent):
    """订单事件"""
    EVENT_TYPE: ClassVar[str] = "order"

    def __init__(self, data: OrderData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class TradeEvent(BaseEvent):
    """成交事件"""
    EVENT_TYPE: ClassVar[str] = "trade"

    def __init__(self, data: TradeData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class OrderUpdateEvent(BaseEvent):
    """订单状态更新事件"""
    EVENT_TYPE: ClassVar[str] = "order_update"

    def __init__(self, data: OrderData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class FillEvent(BaseEvent):
    """成交回报事件"""
    EVENT_TYPE: ClassVar[str] = "fill"

    def __init__(self, data: TradeData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


# 投资组合事件
class PositionUpdateEvent(BaseEvent):
    """持仓更新事件"""
    EVENT_TYPE: ClassVar[str] = "position_update"

    def __init__(self, data: PositionData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class AccountUpdateEvent(BaseEvent):
    """账户更新事件"""
    EVENT_TYPE: ClassVar[str] = "account_update"

    def __init__(self, data: AccountData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class OrderRequestEvent(BaseEvent):
    """订单请求事件"""
    EVENT_TYPE: ClassVar[str] = "order_request"

    def __init__(self, data: OrderData, **kwargs):
        super().__init__(**kwargs)
        self.data = data


class OrderRejectedEvent(BaseEvent):
    """订单拒绝事件"""
    EVENT_TYPE: ClassVar[str] = "order_rejected"

    def __init__(self, order: OrderData, reason: str, **kwargs):
        super().__init__(**kwargs)
        self.order = order
        self.reason = reason


# 系统事件
class LogEvent(BaseEvent):
    """日志事件"""
    EVENT_TYPE: ClassVar[str] = "log"

    def __init__(self, level: str, message: str, module: str = "system",
                 extra_data: Dict[str, Any] = None, **kwargs):
        super().__init__(**kwargs)
        self.level = level
        self.message = message
        self.module = module
        self.extra_data = extra_data or {}


class RiskWarningEvent(BaseEvent):
    """风险警告事件"""
    EVENT_TYPE: ClassVar[str] = "risk_warning"

    def __init__(self, risk_type: str, message: str, severity: str = "WARNING",
                 data: Dict[str, Any] = None, **kwargs):
        super().__init__(**kwargs)
        self.risk_type = risk_type
        self.message = message
        self.severity = severity
        self.data = data or {}


class SystemStatusEvent(BaseEvent):
    """系统状态事件"""
    EVENT_TYPE: ClassVar[str] = "system_status"

    def __init__(self, status: str, message: str = "", component: str = "system", **kwargs):
        super().__init__(**kwargs)
        self.status = status
        self.message = message
        self.component = component


class StrategyStatusEvent(BaseEvent):
    """策略状态事件"""
    EVENT_TYPE: ClassVar[str] = "strategy_status"

    def __init__(self, strategy_name: str, status: str, message: str = "", **kwargs):
        super().__init__(**kwargs)
        self.strategy_name = strategy_name
        self.status = status
        self.message = message


# 数据服务事件
class DataServiceStatusEvent(BaseEvent):
    """数据服务状态事件"""
    EVENT_TYPE: ClassVar[str] = "data_service_status"

    def __init__(self, service_name: str, status: str, message: str = "", **kwargs):
        super().__init__(**kwargs)
        self.service_name = service_name
        self.status = status
        self.message = message


# 通知事件
class NotificationEvent(BaseEvent):
    """通知事件"""
    EVENT_TYPE: ClassVar[str] = "notification"

    def __init__(self, title: str, message: str, notification_type: str = "info",
                 urgent: bool = False, **kwargs):
        super().__init__(**kwargs)
        self.title = title
        self.message = message
        self.notification_type = notification_type
        self.urgent = urgent


# 事件类型注册表
EVENT_TYPE_REGISTRY: Dict[str, Type[BaseEvent]] = {
    "tick": TickEvent,
    "bar": BarEvent,
    "signal": SignalEvent,
    "order": OrderEvent,
    "trade": TradeEvent,
    "order_update": OrderUpdateEvent,
    "fill": FillEvent,
    "position_update": PositionUpdateEvent,
    "account_update": AccountUpdateEvent,
    "order_request": OrderRequestEvent,
    "order_rejected": OrderRejectedEvent,
    "log": LogEvent,
    "risk_warning": RiskWarningEvent,
    "system_status": SystemStatusEvent,
    "strategy_status": StrategyStatusEvent,
    "data_service_status": DataServiceStatusEvent,
    "notification": NotificationEvent,
}


def get_event_class(event_type: str) -> Type[BaseEvent]:
    """根据事件类型获取事件类"""
    if event_type not in EVENT_TYPE_REGISTRY:
        raise ValueError(f"未知的事件类型: {event_type}")
    return EVENT_TYPE_REGISTRY[event_type]


def register_event_type(event_class: Type[BaseEvent]) -> None:
    """注册新的事件类型"""
    event_type = event_class.EVENT_TYPE
    if event_type in EVENT_TYPE_REGISTRY:
        raise ValueError(f"事件类型 {event_type} 已经注册")
    EVENT_TYPE_REGISTRY[event_type] = event_class
