#!/usr/bin/env python3
"""
向运行中的系统发送测试数据
用于验证GUI是否正常工作
"""

import sys
import time
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.event_types import TickEvent, BarEvent, OrderEvent, TradeEvent, PositionUpdateEvent, AccountUpdateEvent
from core.data_types import (
    TickData, BarData, OrderData, TradeData, PositionData, AccountData,
    Direction, OrderType, OrderStatus, Interval
)
from core.event_bus import EventBus


def send_test_data():
    """发送测试数据到运行中的系统"""
    print("🧪 准备发送测试数据...")
    
    # 这里我们创建一个简单的事件总线来发送数据
    # 在实际应用中，这些数据会通过网络或其他方式发送
    
    # 模拟账户数据
    account_data = AccountData(
        account_id="test_account_001",
        balance=Decimal('1000000.00'),
        available=Decimal('800000.00'),
        frozen=Decimal('200000.00')
    )
    print(f"📊 账户余额: {account_data.balance}")
    
    # 模拟持仓数据
    position_data = PositionData(
        symbol="AAPL",
        direction=Direction.LONG,
        volume=1000,
        price=Decimal('186.50'),
        pnl=Decimal('3500.00')
    )
    print(f"📈 持仓: {position_data.symbol} {position_data.volume}股")
    
    # 模拟订单数据
    order_data = OrderData(
        symbol="AAPL",
        order_id="ORDER_001",
        direction=Direction.LONG,
        order_type=OrderType.LIMIT,
        volume=500,
        price=Decimal('187.00'),
        status=OrderStatus.FILLED,
        datetime=datetime.now()
    )
    print(f"📋 订单: {order_data.symbol} {order_data.volume}股 @{order_data.price}")
    
    # 模拟成交数据
    trade_data = TradeData(
        symbol="AAPL",
        trade_id="TRADE_001",
        order_id="ORDER_001",
        direction=Direction.LONG,
        volume=500,
        price=Decimal('187.00'),
        datetime=datetime.now()
    )
    print(f"💰 成交: {trade_data.symbol} {trade_data.volume}股 @{trade_data.price}")
    
    # 模拟实时价格数据
    for i in range(5):
        price = 186.0 + i * 0.1
        tick_data = TickData(
            symbol="AAPL",
            last_price=Decimal(f'{price:.2f}'),
            volume=1000 + i * 100,
            datetime=datetime.now()
        )
        print(f"📊 Tick {i+1}: {tick_data.symbol} @{tick_data.last_price}")
        time.sleep(1)
    
    print("✅ 测试数据发送完成")
    print("💡 如果GUI窗口正在运行，您应该能看到数据更新")


if __name__ == "__main__":
    send_test_data()
