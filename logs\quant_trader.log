2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 07:51:16 - main_controller - [32m<PERSON><PERSON><PERSON>[0m - 事件总线初始化完成
2025-07-11 07:51:16 - main_controller - [32m<PERSON><PERSON><PERSON>[0m - 启动模块: infrastructure
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: strategy_engine
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 模块 strategy_engine 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: execution_portfolio
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 模块 execution_portfolio 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: gui
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 07:51:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:14:51 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:14:51 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:20:14 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:20:14 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:38:36 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:36 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:38:37 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:37 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:42:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:43:42 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:44:43 - main_controller - [31mERROR[0m - 通知服务初始化失败: 'ConfigManager' object has no attribute 'get_config'
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:44:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:44:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:44:45 - main_controller - [31mERROR[0m - 模块 strategy_engine 启动失败: cannot import name 'Backtester' from 'strategy_ai_engine.backtester' (E:\Quant_traders\Gemini_quant\strategy_ai_engine\backtester.py)
2025-07-11 09:44:45 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'Backtester' from 'strategy_ai_engine.backtester' (E:\Quant_traders\Gemini_quant\strategy_ai_engine\backtester.py)
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:47:10 - main_controller - [31mERROR[0m - 模块 execution_portfolio 启动失败: cannot import name 'OrderRequestEvent' from 'core.event_types' (E:\Quant_traders\Gemini_quant\core\event_types.py)
2025-07-11 09:47:10 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'OrderRequestEvent' from 'core.event_types' (E:\Quant_traders\Gemini_quant\core\event_types.py)
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 09:49:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:05:17 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:06:33 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:10:33 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:35 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:36 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:36 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:12:41 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:15:41 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:28 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:34:39 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:51 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:51 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:52 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:35:54 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:35:54 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:41:12 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:50:43 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:50:43 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:51:32 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:51:32 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:52:30 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:30 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:52:53 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:53 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:53:38 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:53:38 - main_controller - [31mERROR[0m - 系统启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:54:11 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:54:11 - main_controller - [31mERROR[0m - 系统启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:56:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:56:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 14:11:29 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 14:11:29 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 14:47:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 14:47:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 19:07:54 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 19:07:54 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 21:25:39 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'MainWindow' object has no attribute '_show_crypto_list'
2025-07-11 21:25:39 - main_controller - [31mERROR[0m - 系统启动失败: 'MainWindow' object has no attribute '_show_crypto_list'
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 21:30:55 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-11 21:30:55 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 07:39:44 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 07:39:44 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 08:01:11 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 08:01:11 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 09:43:50 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:43:50 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 09:46:04 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:46:04 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:10:26 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:10:26 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:12:35 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:35 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:12:49 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:49 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:31:43 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:31:43 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:34:20 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:34:20 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:37:00 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:37:00 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - PySide6不可用，跳过GUI模块
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 11:39:01 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:07 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
