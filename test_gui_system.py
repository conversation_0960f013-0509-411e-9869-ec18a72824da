#!/usr/bin/env python3
"""
GUI系统测试
测试完整的GUI界面，包括K线图表显示
"""

import sys
import time
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# 检查GUI库
try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    GUI_AVAILABLE = True
except ImportError:
    print("PySide6未安装，将使用控制台模式")
    GUI_AVAILABLE = False

from core.main_controller import MainController
from core.event_types import TickEvent, BarEvent, OrderEvent, TradeEvent, PositionUpdateEvent, AccountUpdateEvent
from core.data_types import (
    TickData, BarData, OrderData, TradeData, PositionData, AccountData,
    Direction, OrderType, OrderStatus, Interval
)
from infrastructure.logger import setup_logger


class GUISystemTester:
    """GUI系统测试器"""
    
    def __init__(self):
        self.logger = setup_logger("gui_test")
        self.controller = None
        self.app = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        self.logger.info("设置测试环境...")
        
        # 确保数据目录存在
        data_dir = PROJECT_ROOT / "data" / "csv"
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # 确保日志目录存在
        log_dir = PROJECT_ROOT / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
    def create_test_data(self):
        """创建测试数据"""
        self.logger.info("创建测试数据...")
        
        # 创建AAPL测试数据（如果不存在）
        aapl_file = PROJECT_ROOT / "data" / "csv" / "AAPL_1m.csv"
        if not aapl_file.exists():
            self.logger.info("创建AAPL测试数据文件...")
            
            # 生成30分钟的测试数据
            start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
            data_lines = ["datetime,open,high,low,close,volume,turnover"]
            
            base_price = 185.0
            for i in range(30):
                dt = start_time + timedelta(minutes=i)
                
                # 模拟价格波动
                open_price = base_price + (i * 0.1) + (i % 3 - 1) * 0.05
                high_price = open_price + 0.2 + (i % 2) * 0.1
                low_price = open_price - 0.15 - (i % 2) * 0.05
                close_price = open_price + (i % 5 - 2) * 0.08
                volume = 1000000 + i * 10000
                turnover = volume * close_price
                
                line = f"{dt.strftime('%Y-%m-%d %H:%M:%S')},{open_price:.2f},{high_price:.2f},{low_price:.2f},{close_price:.2f},{volume},{turnover:.0f}"
                data_lines.append(line)
            
            with open(aapl_file, 'w') as f:
                f.write('\n'.join(data_lines))
            
            self.logger.info(f"创建了{len(data_lines)-1}条AAPL测试数据")
    
    def start_gui_system(self):
        """启动GUI系统"""
        self.logger.info("启动GUI系统...")
        
        try:
            if GUI_AVAILABLE:
                # 创建QApplication
                self.app = QApplication(sys.argv)
                self.app.setApplicationName("Gemini Quant")
                self.app.setApplicationVersion("1.0")
                
                self.logger.info("QApplication创建成功")
            
            # 创建主控制器
            self.controller = MainController()
            
            # 启动系统
            self.controller.start()
            
            # 等待系统启动完成
            time.sleep(2)
            
            # 发送测试数据
            self.send_test_events()
            
            if GUI_AVAILABLE and self.app:
                self.logger.info("启动GUI事件循环...")
                
                # 设置定时器发送更多测试数据
                self.setup_data_timer()
                
                # 运行GUI事件循环
                return self.app.exec()
            else:
                self.logger.info("控制台模式运行...")
                # 控制台模式，保持运行
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    self.logger.info("收到中断信号")
                    
        except Exception as e:
            self.logger.error(f"启动GUI系统失败: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            if self.controller:
                self.controller.stop()
    
    def setup_data_timer(self):
        """设置数据定时器"""
        if not GUI_AVAILABLE:
            return
            
        # 创建定时器，每5秒发送一次新数据
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.send_realtime_data)
        self.data_timer.start(5000)  # 5秒间隔
        
        self.logger.info("数据定时器已启动")
    
    def send_test_events(self):
        """发送测试事件"""
        if not self.controller or not self.controller._event_bus:
            self.logger.warning("事件总线未就绪")
            return
            
        self.logger.info("发送测试事件...")
        
        try:
            event_bus = self.controller._event_bus
            
            # 1. 发送账户更新事件
            account_data = AccountData(
                account_id="test_account",
                balance=Decimal('1000000'),
                available=Decimal('800000'),
                frozen=Decimal('200000')
            )
            account_event = AccountUpdateEvent(account_data)
            event_bus.publish(account_event)
            
            # 2. 发送持仓更新事件
            position_data = PositionData(
                symbol="AAPL",
                direction=Direction.LONG,
                volume=1000,
                price=Decimal('185.50'),
                pnl=Decimal('2500.00')
            )
            position_event = PositionUpdateEvent(position_data)
            event_bus.publish(position_event)
            
            # 3. 发送订单事件
            order_data = OrderData(
                symbol="AAPL",
                order_id="test_order_001",
                direction=Direction.LONG,
                order_type=OrderType.LIMIT,
                volume=500,
                price=Decimal('186.00'),
                status=OrderStatus.FILLED,
                datetime=datetime.now()
            )
            order_event = OrderEvent(order_data)
            event_bus.publish(order_event)
            
            # 4. 发送成交事件
            trade_data = TradeData(
                symbol="AAPL",
                trade_id="test_trade_001",
                order_id="test_order_001",
                direction=Direction.LONG,
                volume=500,
                price=Decimal('186.00'),
                datetime=datetime.now()
            )
            trade_event = TradeEvent(trade_data)
            event_bus.publish(trade_event)
            
            # 5. 发送Tick事件
            tick_data = TickData(
                symbol="AAPL",
                last_price=Decimal('186.25'),
                volume=1500,
                datetime=datetime.now()
            )
            tick_event = TickEvent(tick_data)
            event_bus.publish(tick_event)
            
            # 6. 发送K线事件
            bar_data = BarData(
                symbol="AAPL",
                datetime=datetime.now(),
                interval=Interval.MINUTE_1,
                open_price=Decimal('186.00'),
                high_price=Decimal('186.50'),
                low_price=Decimal('185.80'),
                close_price=Decimal('186.25'),
                volume=2000
            )
            bar_event = BarEvent(bar_data)
            event_bus.publish(bar_event)
            
            self.logger.info("测试事件发送完成")
            
        except Exception as e:
            self.logger.error(f"发送测试事件失败: {e}")
    
    def send_realtime_data(self):
        """发送实时数据"""
        if not self.controller or not self.controller._event_bus:
            return
            
        try:
            event_bus = self.controller._event_bus
            
            # 生成随机价格变动
            import random
            base_price = 186.0
            price_change = random.uniform(-0.5, 0.5)
            new_price = base_price + price_change
            
            # 发送新的Tick数据
            tick_data = TickData(
                symbol="AAPL",
                last_price=Decimal(f'{new_price:.2f}'),
                volume=random.randint(1000, 5000),
                datetime=datetime.now()
            )
            tick_event = TickEvent(tick_data)
            event_bus.publish(tick_event)
            
            # 偶尔发送K线数据
            if random.random() < 0.3:  # 30%概率
                bar_data = BarData(
                    symbol="AAPL",
                    datetime=datetime.now(),
                    interval=Interval.MINUTE_1,
                    open_price=Decimal(f'{new_price-0.1:.2f}'),
                    high_price=Decimal(f'{new_price+0.2:.2f}'),
                    low_price=Decimal(f'{new_price-0.2:.2f}'),
                    close_price=Decimal(f'{new_price:.2f}'),
                    volume=random.randint(1500, 3000)
                )
                bar_event = BarEvent(bar_data)
                event_bus.publish(bar_event)
            
        except Exception as e:
            self.logger.error(f"发送实时数据失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Gemini Quant GUI系统测试")
    print("=" * 60)
    
    if GUI_AVAILABLE:
        print("✅ GUI库可用，将启动图形界面")
        print("📊 包含K线图表、交易面板、投资组合等完整功能")
    else:
        print("⚠️  GUI库不可用，将使用控制台模式")
        print("💡 要使用GUI模式，请安装: pip install PySide6")
    
    print("=" * 60)
    
    # 创建测试器
    tester = GUISystemTester()
    
    # 设置测试环境
    tester.setup_test_environment()
    
    # 创建测试数据
    tester.create_test_data()
    
    # 启动GUI系统
    return tester.start_gui_system()


if __name__ == "__main__":
    sys.exit(main())
