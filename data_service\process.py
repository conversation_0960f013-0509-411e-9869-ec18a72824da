"""
数据服务进程
运行在独立进程中的数据服务，负责数据接入、处理和存储
"""

from __future__ import annotations
import multiprocessing as mp
import threading
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

from .connectors.base_connector import BaseConnector
from .connectors.csv_connector import CSVConnector
from .processor import DataProcessor
from .storage import DataStorage
from core.event_types import BaseEvent, TickEvent, BarEvent, DataServiceStatusEvent
from core.data_types import Interval


class DataServiceProcess:
    """
    数据服务进程

    特性：
    1. 独立进程运行，避免GIL限制
    2. 多数据源支持
    3. 实时数据处理和K线合成
    4. 高效数据存储
    5. 进程间通信
    """

    def __init__(self, config: Any):
        self.config = config
        self.event_bus = None  # 在子进程中初始化
        self.logger = logging.getLogger(__name__)

        # 数据服务配置
        self.data_config = config.get('data_service', {})
        self.storage_path = self.data_config.get('storage_path', './data')
        self.storage_type = self.data_config.get('storage_type', 'feather')

        # 核心组件
        self.connectors: Dict[str, BaseConnector] = {}
        self.processor: Optional[DataProcessor] = None
        self.storage: Optional[DataStorage] = None

        # 运行状态
        self._running = False
        self._process: Optional[mp.Process] = None
        self._event_queue: Optional[mp.Queue] = None

        # 统计信息
        self.stats = {
            'connected_sources': 0,
            'processed_ticks': 0,
            'generated_bars': 0,
            'stored_records': 0
        }

    def start(self) -> None:
        """启动数据服务"""
        if self._running:
            self.logger.warning("数据服务已在运行")
            return

        try:
            self.logger.info("启动数据服务进程...")

            # 创建进程间通信队列
            self._event_queue = mp.Queue(maxsize=10000)

            # 启动数据服务进程
            self._process = mp.Process(
                target=self._run_data_service,
                args=(self.config, self._event_queue),
                name="DataService",
                daemon=True
            )
            self._process.start()

            # 启动事件桥接器
            self._start_event_bridge()

            self._running = True
            self.logger.info("数据服务进程启动成功")

        except Exception as e:
            self.logger.error(f"启动数据服务失败: {e}")
            raise

    def stop(self) -> None:
        """停止数据服务"""
        if not self._running:
            return

        try:
            self.logger.info("停止数据服务进程...")
            self._running = False

            # 停止数据服务进程
            if self._process and self._process.is_alive():
                self._process.terminate()
                self._process.join(timeout=5)
                if self._process.is_alive():
                    self._process.kill()

            # 清理资源
            if self._event_queue:
                self._event_queue.close()

            self.logger.info("数据服务进程已停止")

        except Exception as e:
            self.logger.error(f"停止数据服务失败: {e}")

    def _run_data_service(self, config: Any, event_queue: mp.Queue) -> None:
        """数据服务主循环（在独立进程中运行）"""
        try:
            # 在子进程中重新初始化
            self.config = config
            self._event_queue = event_queue
            self.logger = logging.getLogger(__name__)

            # 重新设置日志（子进程需要独立的日志配置）
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            logger = logging.getLogger("DataService")
            logger.info("数据服务进程开始运行")

            # 初始化组件
            self._init_components()

            # 启动数据连接器
            self._start_connectors()

            # 主循环
            while True:
                try:
                    time.sleep(1)  # 保持进程活跃

                    # 这里可以添加定期任务
                    # 例如：数据清理、统计报告等

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"数据服务主循环异常: {e}")

            # 清理资源
            self._cleanup_components()
            logger.info("数据服务进程结束")

        except Exception as e:
            print(f"数据服务进程异常: {e}")

    def _init_components(self) -> None:
        """初始化数据服务组件"""
        # 初始化数据存储
        self.storage = DataStorage(
            storage_path=self.storage_path,
            storage_type=self.storage_type
        )

        # 初始化数据处理器
        self.processor = DataProcessor(
            event_callback=self._on_processed_event
        )

        # 初始化数据连接器
        self._init_connectors()

    def _init_connectors(self) -> None:
        """初始化数据连接器"""
        connectors_config = self.data_config.get('connectors', {})

        for connector_name, connector_config in connectors_config.items():
            if not connector_config.get('enabled', False):
                continue

            try:
                if connector_name == 'csv':
                    connector = CSVConnector(connector_name, connector_config)
                else:
                    continue  # 跳过未实现的连接器

                # 设置事件回调
                connector.set_event_callback(self._on_connector_event)
                self.connectors[connector_name] = connector

            except Exception as e:
                print(f"初始化连接器 {connector_name} 失败: {e}")

    def _start_connectors(self) -> None:
        """启动所有数据连接器"""
        for name, connector in self.connectors.items():
            try:
                if connector.connect():
                    print(f"连接器 {name} 启动成功")
                    self.stats['connected_sources'] += 1
                else:
                    print(f"连接器 {name} 启动失败")
            except Exception as e:
                print(f"启动连接器 {name} 异常: {e}")

    def _cleanup_components(self) -> None:
        """清理组件资源"""
        # 断开所有连接器
        for connector in self.connectors.values():
            try:
                connector.disconnect()
            except:
                pass

        # 关闭存储
        if self.storage:
            try:
                self.storage.close()
            except:
                pass

    def _on_connector_event(self, event: BaseEvent) -> None:
        """处理连接器事件"""
        try:
            # 处理Tick数据
            if isinstance(event, TickEvent):
                if self.processor:
                    generated_bars = self.processor.process_tick(event.data)
                    self.stats['processed_ticks'] += 1

                    # 保存Tick数据
                    if self.storage:
                        self.storage.save_tick_data([event.data])
                        self.stats['stored_records'] += 1

                    # 保存生成的K线数据
                    if generated_bars and self.storage:
                        self.storage.save_bar_data(generated_bars)
                        self.stats['stored_records'] += len(generated_bars)
                        self.stats['generated_bars'] += len(generated_bars)

            # 转发事件到主进程
            if self._event_queue:
                try:
                    self._event_queue.put_nowait(event)
                except:
                    pass  # 队列满时忽略

        except Exception as e:
            print(f"处理连接器事件失败: {e}")

    def _on_processed_event(self, event: BaseEvent) -> None:
        """处理数据处理器事件"""
        # 转发事件到主进程
        if self._event_queue:
            try:
                self._event_queue.put_nowait(event)
            except:
                pass

    def _start_event_bridge(self) -> None:
        """启动事件桥接器"""
        def bridge_worker():
            """事件桥接工作线程"""
            while self._running:
                try:
                    if self._event_queue:
                        event = self._event_queue.get(timeout=1.0)
                        if event and self.event_bus:
                            self.event_bus.publish(event)
                except:
                    continue

        bridge_thread = threading.Thread(
            target=bridge_worker,
            name="DataService-EventBridge",
            daemon=True
        )
        bridge_thread.start()

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def subscribe_symbol(self, symbol: str, intervals: List[str] = None) -> bool:
        """订阅品种数据"""
        try:
            # 在所有连接器中订阅
            success = False
            for connector in self.connectors.values():
                if connector.subscribe(symbol):
                    success = True

            # 注册K线合成器
            if success and self.processor and intervals:
                for interval_str in intervals:
                    try:
                        interval = Interval(interval_str)
                        self.processor.register_bar_generator(symbol, interval)
                    except ValueError:
                        continue

            return success

        except Exception as e:
            self.logger.error(f"订阅品种 {symbol} 失败: {e}")
            return False
