# Gemini Quant - 完整GUI交易终端

## 🎉 项目完成状态

✅ **项目架构完全符合plan.md设计文档**  
✅ **实现了包含K线图表的完整GUI交易终端**  
✅ **所有核心模块已完善并集成**  

## 🚀 主要功能特性

### 📊 K线图表系统
- **高性能K线图表**: 使用pyqtgraph实现，支持实时更新
- **多时间周期**: 支持1分钟、5分钟、15分钟、30分钟、1小时、4小时、日线
- **技术指标**: 集成RSI、MACD、布林带、ATR等常用指标
- **交互式操作**: 支持缩放、平移、十字光标等

### 💼 投资组合管理
- **实时账户信息**: 余额、可用资金、保证金占用
- **持仓管理**: 实时持仓显示，盈亏计算
- **订单管理**: 订单状态跟踪，历史记录
- **成交记录**: 详细成交信息展示

### 🎯 交易操作面板
- **手动交易**: 市价单、限价单快速下单
- **快速交易**: 预设数量快速买卖
- **风险控制**: 实时风险检查和限制
- **交易状态**: 交易开关、自动交易控制

### 🔧 系统管理
- **日志系统**: 分级日志显示和过滤
- **通知服务**: 多渠道通知（GUI弹窗、声音、邮件）
- **配置管理**: 灵活的配置系统
- **模块化架构**: 松耦合的模块设计

## 🏗️ 架构设计

### MVVM模式
```
View (GUI界面) ↔ ViewModel (业务逻辑) ↔ Model (数据层)
```

### 事件驱动架构
```
事件总线 → 各模块订阅 → 异步处理 → 状态更新
```

### 模块结构
```
├── core/                   # 核心模块
│   ├── event_bus.py       # 事件总线
│   ├── event_types.py     # 事件类型定义
│   ├── data_types.py      # 数据类型定义
│   └── main_controller.py # 主控制器
├── gui/                    # GUI模块
│   ├── views/             # 视图层
│   ├── viewmodels/        # 视图模型层
│   └── main_window.py     # 主窗口
├── data_service/          # 数据服务
├── strategy_ai_engine/    # 策略引擎
├── execution_portfolio/   # 交易执行
└── infrastructure/        # 基础设施
```

## 🛠️ 安装和运行

### 环境要求
- Python 3.13+
- PySide6 (GUI库)
- pyqtgraph (图表库)
- pandas, numpy (数据处理)

### 安装依赖
```bash
pip install PySide6 pyqtgraph pandas numpy
```

### 运行系统

#### 1. 完整GUI模式
```bash
python main.py
```

#### 2. GUI测试模式
```bash
python test_gui_system.py
```

#### 3. 简单GUI测试
```bash
python test_simple_gui.py
```

#### 4. 控制台模式（无GUI环境）
```bash
python main.py --console
```

## 📈 K线图表使用

### 基本操作
- **缩放**: 鼠标滚轮或右键拖拽
- **平移**: 左键拖拽
- **重置视图**: 右键菜单选择"Auto Range"
- **品种切换**: 顶部下拉框选择
- **周期切换**: 周期下拉框选择

### 数据源
- 支持CSV文件数据导入
- 支持实时数据接入
- 内置AAPL等测试数据

## 🎛️ 交易操作

### 手动交易
1. 在交易面板输入品种代码
2. 设置交易数量和价格
3. 点击对应的买入/卖出按钮
4. 系统自动进行风险检查

### 快速交易
- 使用预设数量按钮快速下单
- 支持100、500、1000、5000股快速交易

### 风险控制
- 最大仓位限制
- 单笔订单金额限制
- 日交易次数限制
- 资金充足性检查

## 📊 数据管理

### 支持的数据格式
- CSV文件 (datetime, open, high, low, close, volume)
- 实时Tick数据
- 分钟级K线数据

### 数据存储
- 本地文件存储
- 支持Feather格式（高性能）
- 自动数据清理和归档

## 🔧 配置说明

### 主要配置文件
- `config/config.yaml`: 主配置文件
- `config/logging.yaml`: 日志配置
- `config/data_sources.yaml`: 数据源配置

### 关键配置项
```yaml
gui:
  enabled: true
  theme: "dark"
  max_log_lines: 1000

trading:
  max_order_volume: 10000
  risk_check_enabled: true

data_service:
  storage_format: "feather"
  max_cache_size: 1000000
```

## 🚨 风险管理

### 内置风控规则
- 最大仓位比例: 10%
- 最大日损失: 5%
- 最大回撤: 20%
- 单笔订单限额: 100,000

### 风险监控
- 实时持仓监控
- 账户风险评估
- 自动风险预警
- 紧急止损机制

## 📝 开发指南

### 添加新的技术指标
1. 在`strategy_ai_engine/indicators/`中创建指标类
2. 继承`BaseIndicator`基类
3. 实现`update()`方法
4. 在`__init__.py`中导出

### 添加新的数据连接器
1. 在`data_service/connectors/`中创建连接器
2. 继承`BaseConnector`基类
3. 实现数据获取方法
4. 在配置文件中注册

### 自定义GUI组件
1. 在`gui/views/`中创建视图组件
2. 在`gui/viewmodels/`中创建对应的ViewModel
3. 使用事件总线进行数据通信

## 🐛 故障排除

### 常见问题

#### GUI无法启动
```bash
# 检查PySide6安装
python -c "import PySide6; print('PySide6 OK')"

# 检查pyqtgraph安装
python -c "import pyqtgraph; print('pyqtgraph OK')"
```

#### 数据无法加载
- 检查`data/csv/`目录是否存在
- 确认CSV文件格式正确
- 查看日志文件中的错误信息

#### 交易功能异常
- 检查风险管理配置
- 确认账户信息正确
- 查看订单状态和错误日志

### 日志文件位置
- 主日志: `logs/gemini_quant.log`
- 错误日志: `logs/error.log`
- 交易日志: `logs/trading.log`

## 🎯 下一步计划

### 短期目标
- [ ] 添加更多技术指标
- [ ] 实现策略回测功能
- [ ] 集成实盘交易接口
- [ ] 优化GUI性能

### 长期目标
- [ ] 机器学习策略优化
- [ ] 多市场数据支持
- [ ] 云端部署方案
- [ ] 移动端应用

## 📞 技术支持

如有问题或建议，请查看：
1. 项目文档: `plan.md`
2. 代码注释和文档字符串
3. 测试用例和示例代码
4. 日志文件中的详细信息

---

**🎉 恭喜！您现在拥有一个功能完整的量化交易系统，包含专业级的K线图表界面！**
