#!/usr/bin/env python3
"""
数据服务模块简化测试
不依赖外部库，只测试核心逻辑
"""

import sys
import time
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from data_service.processor import DataProcessor, BarGenerator
from core.data_types import Interval, TickData, BarData
from core.event_types import TickEvent, BarEvent
from infrastructure.logger import setup_logger
from decimal import Decimal


def test_bar_generator():
    """测试K线合成器"""
    print("测试K线合成器...")
    
    generated_bars = []
    
    def bar_callback(bar):
        generated_bars.append(bar)
        print(f"生成K线: {bar.symbol} {bar.datetime} O:{bar.open_price} C:{bar.close_price}")
    
    generator = BarGenerator('TEST', Interval.MINUTE_1, bar_callback)
    
    # 模拟Tick数据
    base_time = datetime(2024, 1, 2, 9, 30)
    
    # 第一分钟的Tick
    for i in range(5):
        tick = TickData(
            symbol='TEST',
            datetime=base_time.replace(second=i*10),
            last_price=Decimal(f'100.{i:02d}'),
            volume=100,
            turnover=Decimal(f'10000.{i:02d}')
        )
        generator.update_tick(tick)
    
    # 第二分钟的Tick（应该触发K线生成）
    tick = TickData(
        symbol='TEST',
        datetime=base_time.replace(minute=31, second=0),
        last_price=Decimal('101.00'),
        volume=100,
        turnover=Decimal('10100.00')
    )
    generator.update_tick(tick)
    
    # 强制完成当前K线
    generator.force_complete_bar()
    
    assert len(generated_bars) == 2, f"应该生成2根K线，实际生成{len(generated_bars)}根"
    
    # 验证第一根K线
    first_bar = generated_bars[0]
    assert first_bar.symbol == 'TEST'
    assert first_bar.open_price == Decimal('100.00')
    assert first_bar.close_price == Decimal('100.04')
    assert first_bar.high_price == Decimal('100.04')
    assert first_bar.low_price == Decimal('100.00')
    
    print("✓ K线合成器测试通过")


def test_data_processor():
    """测试数据处理器"""
    print("测试数据处理器...")
    
    received_events = []
    
    def event_callback(event):
        received_events.append(event)
        print(f"收到事件: {event.__class__.__name__}")
    
    processor = DataProcessor(event_callback)
    
    # 注册K线合成器
    gen1_id = processor.register_bar_generator('TEST', Interval.MINUTE_1)
    gen2_id = processor.register_bar_generator('TEST', Interval.MINUTE_5)
    
    print(f"注册了K线合成器: {gen1_id}, {gen2_id}")
    
    # 模拟处理Tick数据
    base_time = datetime(2024, 1, 2, 9, 30)
    
    for i in range(10):
        tick = TickData(
            symbol='TEST',
            datetime=base_time.replace(second=i*6),
            last_price=Decimal(f'100.{i:02d}'),
            volume=100,
            turnover=Decimal(f'10000.{i:02d}')
        )
        bars = processor.process_tick(tick)
        if bars:
            print(f"处理Tick生成了{len(bars)}根K线")
    
    # 触发新分钟（应该生成1分钟K线）
    tick = TickData(
        symbol='TEST',
        datetime=base_time.replace(minute=31),
        last_price=Decimal('101.00'),
        volume=100,
        turnover=Decimal('10100.00')
    )
    bars = processor.process_tick(tick)
    print(f"新分钟Tick生成了{len(bars)}根K线")
    
    # 获取统计信息
    stats = processor.get_statistics()
    print(f"处理统计: {stats}")
    
    assert stats['processed_ticks'] == 11, f"处理的Tick数量不正确: {stats['processed_ticks']}"
    assert stats['generated_bars'] >= 1, f"生成的K线数量不正确: {stats['generated_bars']}"
    
    # 测试获取当前K线
    current_bars = processor.get_current_bars()
    print(f"当前未完成K线数量: {len(current_bars)}")
    
    # 强制完成所有K线
    completed_bars = processor.force_complete_all_bars()
    print(f"强制完成了{len(completed_bars)}根K线")
    
    print("✓ 数据处理器测试通过")


def test_data_types():
    """测试数据类型"""
    print("测试数据类型...")
    
    # 测试TickData
    tick = TickData(
        symbol='TEST',
        datetime=datetime.now(),
        last_price=Decimal('100.50'),
        volume=1000,
        turnover=Decimal('100500.00')
    )
    
    assert tick.symbol == 'TEST'
    assert tick.last_price == Decimal('100.50')
    assert tick.volume == 1000
    
    # 测试BarData
    bar = BarData(
        symbol='TEST',
        datetime=datetime.now(),
        interval=Interval.MINUTE_1,
        open_price=Decimal('100.00'),
        high_price=Decimal('101.00'),
        low_price=Decimal('99.50'),
        close_price=Decimal('100.50'),
        volume=10000,
        turnover=Decimal('1005000.00')
    )
    
    assert bar.symbol == 'TEST'
    assert bar.interval == Interval.MINUTE_1
    assert bar.open_price == Decimal('100.00')
    assert bar.close_price == Decimal('100.50')
    
    print("✓ 数据类型测试通过")


def test_event_types():
    """测试事件类型"""
    print("测试事件类型...")
    
    # 测试TickEvent
    tick = TickData(
        symbol='TEST',
        datetime=datetime.now(),
        last_price=Decimal('100.50'),
        volume=1000,
        turnover=Decimal('100500.00')
    )
    
    tick_event = TickEvent(data=tick, source='test')
    assert tick_event.data == tick
    assert tick_event.source == 'test'
    assert tick_event.get_event_type() == 'tick'
    
    # 测试BarEvent
    bar = BarData(
        symbol='TEST',
        datetime=datetime.now(),
        interval=Interval.MINUTE_1,
        open_price=Decimal('100.00'),
        high_price=Decimal('101.00'),
        low_price=Decimal('99.50'),
        close_price=Decimal('100.50'),
        volume=10000,
        turnover=Decimal('1005000.00')
    )
    
    bar_event = BarEvent(data=bar, source='test')
    assert bar_event.data == bar
    assert bar_event.source == 'test'
    assert bar_event.get_event_type() == 'bar'
    
    print("✓ 事件类型测试通过")


def test_intervals():
    """测试时间间隔"""
    print("测试时间间隔...")
    
    # 测试所有支持的间隔
    intervals = [
        Interval.TICK,
        Interval.MINUTE_1,
        Interval.MINUTE_5,
        Interval.MINUTE_15,
        Interval.MINUTE_30,
        Interval.HOUR_1,
        Interval.HOUR_4,
        Interval.DAY_1,
        Interval.WEEK_1,
        Interval.MONTH_1
    ]
    
    for interval in intervals:
        assert isinstance(interval.value, str)
        print(f"间隔: {interval.value}")
    
    print("✓ 时间间隔测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行数据服务模块简化测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger(
        name='test_data_service_simple',
        level='INFO',
        enable_console=True,
        enable_events=False
    )
    
    try:
        test_data_types()
        test_event_types()
        test_intervals()
        test_bar_generator()
        test_data_processor()
        
        print("=" * 50)
        print("✓ 所有数据服务简化测试通过！")
        print("=" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
