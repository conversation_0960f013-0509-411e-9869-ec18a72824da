"""
日志 ViewModel
负责系统日志的管理和展示
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
from datetime import datetime

from PySide6.QtCore import QObject, Signal

from core.event_types import LogEvent


class LogViewModel(QObject):
    """
    日志ViewModel
    
    职责:
    1. 订阅日志事件
    2. 管理日志消息缓存
    3. 提供日志过滤功能
    4. 发射日志更新信号
    """
    
    # 信号定义
    log_added = Signal(str, str, str)  # timestamp, level, message
    logs_cleared = Signal()
    log_filter_changed = Signal(str)  # filter_level
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        super().__init__()
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 日志存储
        self.log_messages: List[Dict[str, str]] = []
        
        # 配置
        self.max_log_lines = config.get('gui', {}).get('max_log_lines', 1000)
        self.log_filter_level = 'INFO'  # DEBUG, INFO, WARNING, ERROR
        
        # 日志级别映射
        self.level_priority = {
            'DEBUG': 0,
            'INFO': 1,
            'WARNING': 2,
            'ERROR': 3
        }
        
        # 订阅事件
        self._subscribe_events()
        
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("log", self._on_log_event, "log_vm")
    
    def _on_log_event(self, event: LogEvent) -> None:
        """处理日志事件"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            level = event.level.upper()
            message = event.message
            
            # 创建日志条目
            log_entry = {
                'timestamp': timestamp,
                'level': level,
                'message': message,
                'full_timestamp': datetime.now().isoformat()
            }
            
            # 添加到缓存
            self.log_messages.append(log_entry)
            
            # 限制日志数量
            if len(self.log_messages) > self.max_log_lines:
                self.log_messages.pop(0)
            
            # 检查是否需要显示（根据过滤级别）
            if self._should_display_log(level):
                self.log_added.emit(timestamp, level, message)
                
        except Exception as e:
            # 避免日志处理本身出错导致循环
            print(f"处理日志事件失败: {e}")
    
    def _should_display_log(self, level: str) -> bool:
        """检查是否应该显示日志"""
        try:
            current_priority = self.level_priority.get(self.log_filter_level, 1)
            log_priority = self.level_priority.get(level, 1)
            return log_priority >= current_priority
        except:
            return True
    
    def set_filter_level(self, level: str) -> None:
        """设置日志过滤级别"""
        if level in self.level_priority:
            self.log_filter_level = level
            self.log_filter_changed.emit(level)
            self.logger.info(f"日志过滤级别设置为: {level}")
    
    def clear_logs(self) -> None:
        """清空日志"""
        self.log_messages.clear()
        self.logs_cleared.emit()
        self.logger.info("日志已清空")
    
    def get_filtered_logs(self, level_filter: Optional[str] = None) -> List[Dict[str, str]]:
        """获取过滤后的日志"""
        if not level_filter:
            level_filter = self.log_filter_level
            
        filtered_logs = []
        filter_priority = self.level_priority.get(level_filter, 1)
        
        for log_entry in self.log_messages:
            log_priority = self.level_priority.get(log_entry['level'], 1)
            if log_priority >= filter_priority:
                filtered_logs.append(log_entry)
        
        return filtered_logs
    
    def get_recent_logs(self, count: int = 50) -> List[Dict[str, str]]:
        """获取最近的日志"""
        return self.log_messages[-count:] if len(self.log_messages) >= count else self.log_messages
    
    def search_logs(self, keyword: str) -> List[Dict[str, str]]:
        """搜索日志"""
        if not keyword:
            return self.log_messages
        
        keyword_lower = keyword.lower()
        return [
            log for log in self.log_messages 
            if keyword_lower in log['message'].lower()
        ]
    
    def export_logs(self, file_path: str, level_filter: Optional[str] = None) -> bool:
        """导出日志到文件"""
        try:
            logs = self.get_filtered_logs(level_filter)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("时间戳\t级别\t消息\n")
                for log in logs:
                    f.write(f"{log['full_timestamp']}\t{log['level']}\t{log['message']}\n")
            
            self.logger.info(f"日志已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            return False
    
    def get_log_statistics(self) -> Dict[str, int]:
        """获取日志统计信息"""
        stats = {level: 0 for level in self.level_priority.keys()}
        
        for log in self.log_messages:
            level = log['level']
            if level in stats:
                stats[level] += 1
        
        stats['total'] = len(self.log_messages)
        return stats
