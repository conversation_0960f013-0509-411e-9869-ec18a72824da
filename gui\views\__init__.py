"""
GUI Views 模块
实现MVVM架构中的View层
"""

from .chart_view import ChartView
from .portfolio_view import PortfolioView
from .trading_view import TradingView
from .log_view import LogView

# 可选的虚拟币视图
try:
    from .crypto_list_view import CryptoListView
    CRYPTO_AVAILABLE = True
except ImportError:
    CryptoListView = None
    CRYPTO_AVAILABLE = False

__all__ = [
    'ChartView',
    'PortfolioView',
    'TradingView',
    'LogView'
]

if CRYPTO_AVAILABLE:
    __all__.append('CryptoListView')
