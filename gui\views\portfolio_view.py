"""
投资组合视图
显示账户、持仓、订单、成交信息
"""

from __future__ import annotations
from typing import Any, Optional, List
import logging

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QTableWidget, QTableWidgetItem, QLabel, QFrame,
    QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from core.data_types import PositionData, AccountData, OrderData, TradeData
from gui.viewmodels.portfolio_vm import PortfolioViewModel


class AccountInfoWidget(QFrame):
    """账户信息组件"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Box)
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("账户信息")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # 账户数据
        self.balance_label = QLabel("账户余额: --")
        self.available_label = QLabel("可用资金: --")
        self.frozen_label = QLabel("冻结资金: --")
        self.margin_label = QLabel("保证金占用: --")
        self.pnl_label = QLabel("总盈亏: --")
        
        layout.addWidget(self.balance_label)
        layout.addWidget(self.available_label)
        layout.addWidget(self.frozen_label)
        layout.addWidget(self.margin_label)
        layout.addWidget(self.pnl_label)
        
    def update_account(self, account: AccountData) -> None:
        """更新账户信息"""
        self.balance_label.setText(f"账户余额: {account.balance:,.2f}")
        self.available_label.setText(f"可用资金: {account.available:,.2f}")
        self.frozen_label.setText(f"冻结资金: {account.frozen:,.2f}")
        
        # 计算保证金占用率
        margin_ratio = float(account.frozen / account.balance * 100) if account.balance > 0 else 0
        self.margin_label.setText(f"保证金占用: {margin_ratio:.1f}%")


class PositionsTable(QTableWidget):
    """持仓表格"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._init_table()
        
    def _init_table(self) -> None:
        """初始化表格"""
        # 设置列
        headers = ["品种", "方向", "数量", "均价", "现价", "盈亏", "盈亏率"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # 调整列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
    def update_positions(self, positions: List[PositionData]) -> None:
        """更新持仓数据"""
        self.setRowCount(len(positions))
        
        for row, position in enumerate(positions):
            # 品种
            self.setItem(row, 0, QTableWidgetItem(position.symbol))
            
            # 方向
            direction_text = "多头" if position.direction.value == "LONG" else "空头"
            self.setItem(row, 1, QTableWidgetItem(direction_text))
            
            # 数量
            self.setItem(row, 2, QTableWidgetItem(str(position.volume)))
            
            # 均价
            self.setItem(row, 3, QTableWidgetItem(f"{position.price:.2f}"))
            
            # 现价（暂时使用均价）
            self.setItem(row, 4, QTableWidgetItem(f"{position.price:.2f}"))
            
            # 盈亏
            pnl_item = QTableWidgetItem(f"{position.pnl:.2f}")
            if position.pnl > 0:
                pnl_item.setForeground(Qt.green)
            elif position.pnl < 0:
                pnl_item.setForeground(Qt.red)
            self.setItem(row, 5, pnl_item)
            
            # 盈亏率
            pnl_ratio = float(position.pnl / (position.price * position.volume) * 100) if position.price * position.volume > 0 else 0
            ratio_item = QTableWidgetItem(f"{pnl_ratio:.2f}%")
            if pnl_ratio > 0:
                ratio_item.setForeground(Qt.green)
            elif pnl_ratio < 0:
                ratio_item.setForeground(Qt.red)
            self.setItem(row, 6, ratio_item)


class OrdersTable(QTableWidget):
    """订单表格"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._init_table()
        
    def _init_table(self) -> None:
        """初始化表格"""
        headers = ["订单号", "品种", "方向", "类型", "数量", "价格", "状态", "时间"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
    def update_orders(self, orders: List[OrderData]) -> None:
        """更新订单数据"""
        # 显示最近的订单
        display_orders = orders[-50:] if len(orders) > 50 else orders
        self.setRowCount(len(display_orders))
        
        for row, order in enumerate(display_orders):
            # 订单号（显示后8位）
            self.setItem(row, 0, QTableWidgetItem(order.order_id[-8:]))
            
            # 品种
            self.setItem(row, 1, QTableWidgetItem(order.symbol))
            
            # 方向
            direction_text = "买入" if order.direction.value == "LONG" else "卖出"
            self.setItem(row, 2, QTableWidgetItem(direction_text))
            
            # 类型
            self.setItem(row, 3, QTableWidgetItem(order.order_type.value))
            
            # 数量
            self.setItem(row, 4, QTableWidgetItem(str(order.volume)))
            
            # 价格
            self.setItem(row, 5, QTableWidgetItem(f"{order.price:.2f}"))
            
            # 状态
            status_item = QTableWidgetItem(order.status.value)
            if order.status.value == "FILLED":
                status_item.setForeground(Qt.green)
            elif order.status.value == "CANCELLED":
                status_item.setForeground(Qt.red)
            self.setItem(row, 6, status_item)
            
            # 时间
            time_str = order.datetime.strftime("%H:%M:%S")
            self.setItem(row, 7, QTableWidgetItem(time_str))


class TradesTable(QTableWidget):
    """成交表格"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._init_table()
        
    def _init_table(self) -> None:
        """初始化表格"""
        headers = ["成交号", "品种", "方向", "数量", "价格", "金额", "时间"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
    def update_trades(self, trades: List[TradeData]) -> None:
        """更新成交数据"""
        # 显示最近的成交
        display_trades = trades[-50:] if len(trades) > 50 else trades
        self.setRowCount(len(display_trades))
        
        for row, trade in enumerate(display_trades):
            # 成交号（显示后8位）
            self.setItem(row, 0, QTableWidgetItem(trade.trade_id[-8:]))
            
            # 品种
            self.setItem(row, 1, QTableWidgetItem(trade.symbol))
            
            # 方向
            direction_text = "买入" if trade.direction.value == "LONG" else "卖出"
            self.setItem(row, 2, QTableWidgetItem(direction_text))
            
            # 数量
            self.setItem(row, 3, QTableWidgetItem(str(trade.volume)))
            
            # 价格
            self.setItem(row, 4, QTableWidgetItem(f"{trade.price:.2f}"))
            
            # 金额
            amount = float(trade.price * trade.volume)
            self.setItem(row, 5, QTableWidgetItem(f"{amount:.2f}"))
            
            # 时间
            time_str = trade.datetime.strftime("%H:%M:%S")
            self.setItem(row, 6, QTableWidgetItem(time_str))


class PortfolioView(QWidget):
    """
    投资组合视图
    
    功能:
    1. 显示账户信息
    2. 显示持仓列表
    3. 显示订单记录
    4. 显示成交记录
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[PortfolioViewModel] = None
        
        # UI组件
        self.account_widget: Optional[AccountInfoWidget] = None
        self.positions_table: Optional[PositionsTable] = None
        self.orders_table: Optional[OrdersTable] = None
        self.trades_table: Optional[TradesTable] = None
        
        # 初始化UI
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 账户信息
        self.account_widget = AccountInfoWidget()
        layout.addWidget(self.account_widget)
        
        # 标签页
        tab_widget = QTabWidget()
        
        # 持仓标签页
        self.positions_table = PositionsTable()
        tab_widget.addTab(self.positions_table, "持仓")
        
        # 订单标签页
        self.orders_table = OrdersTable()
        tab_widget.addTab(self.orders_table, "订单")
        
        # 成交标签页
        self.trades_table = TradesTable()
        tab_widget.addTab(self.trades_table, "成交")
        
        layout.addWidget(tab_widget)
        
    def set_view_model(self, view_model: PortfolioViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接信号
        self.view_model.account_updated.connect(self._on_account_updated)
        self.view_model.positions_updated.connect(self._on_positions_updated)
        self.view_model.orders_updated.connect(self._on_orders_updated)
        self.view_model.trades_updated.connect(self._on_trades_updated)
        
        self.logger.info("投资组合ViewModel已连接")
        
    def _on_account_updated(self, account: AccountData) -> None:
        """账户更新处理"""
        self.account_widget.update_account(account)
        
    def _on_positions_updated(self, positions: List[PositionData]) -> None:
        """持仓更新处理"""
        self.positions_table.update_positions(positions)
        
    def _on_orders_updated(self, orders: List[OrderData]) -> None:
        """订单更新处理"""
        self.orders_table.update_orders(orders)
        
    def _on_trades_updated(self, trades: List[TradeData]) -> None:
        """成交更新处理"""
        self.trades_table.update_trades(trades)
