# Gemini Quant API 文档

## 核心模块 API

### 事件总线 (EventBus)

#### 基本用法
```python
from core.event_bus import EventBus
from core.event_types import TickEvent

# 创建事件总线
event_bus = EventBus()
event_bus.start()

# 订阅事件
def handle_tick(event):
    print(f"收到Tick: {event.data.symbol} @ {event.data.last_price}")

event_bus.subscribe("tick", handle_tick, "my_handler")

# 发布事件
tick_event = TickEvent(data=tick_data, source="data_source")
event_bus.publish(tick_event)

# 停止事件总线
event_bus.stop()
```

#### API 方法

**subscribe(event_type, handler, handler_id)**
- `event_type`: 事件类型字符串
- `handler`: 事件处理函数
- `handler_id`: 处理器唯一标识

**unsubscribe(event_type, handler_id)**
- 取消订阅指定事件类型的处理器

**publish(event)**
- 发布事件到总线

**get_stats()**
- 返回事件总线统计信息

### 数据类型 (DataTypes)

#### TickData
```python
from core.data_types import TickData
from decimal import Decimal
from datetime import datetime

tick = TickData(
    symbol="AAPL",
    datetime=datetime.now(),
    last_price=Decimal("150.50"),
    volume=1000,
    turnover=Decimal("150500.00"),
    bid_price_1=Decimal("150.49"),
    ask_price_1=Decimal("150.51"),
    bid_volume_1=500,
    ask_volume_1=600
)
```

#### BarData
```python
from core.data_types import BarData, Interval

bar = BarData(
    symbol="AAPL",
    datetime=datetime.now(),
    interval=Interval.MINUTE_1,
    open_price=Decimal("150.00"),
    high_price=Decimal("151.00"),
    low_price=Decimal("149.50"),
    close_price=Decimal("150.50"),
    volume=10000,
    turnover=Decimal("1505000.00")
)
```

#### OrderData
```python
from core.data_types import OrderData, Direction, OrderType, OrderStatus

order = OrderData(
    symbol="AAPL",
    order_id="ORD_001",
    direction=Direction.LONG,
    order_type=OrderType.LIMIT,
    volume=100,
    price=Decimal("150.00"),
    status=OrderStatus.PENDING,
    datetime=datetime.now()
)
```

## 策略开发 API

### 策略基类 (BaseStrategy)

#### 基本结构
```python
from strategies.base_strategy import BaseStrategy
from core.data_types import TickData, BarData

class MyStrategy(BaseStrategy):
    def __init__(self, name, parameters=None):
        super().__init__(name, parameters)
    
    def on_init(self):
        """策略初始化"""
        self.subscribed_symbols = ['AAPL', 'TSLA']
        self.my_indicator = []
    
    def on_start(self):
        """策略启动"""
        self.logger.info("策略启动")
    
    def on_stop(self):
        """策略停止"""
        self.logger.info("策略停止")
    
    def on_tick(self, tick: TickData):
        """处理Tick数据"""
        pass
    
    def on_bar(self, bar: BarData):
        """处理K线数据"""
        # 策略逻辑
        if self.should_buy(bar):
            self.buy(bar.symbol, 100)
    
    def should_buy(self, bar):
        """买入条件"""
        return bar.close_price > bar.open_price
```

#### 核心方法

**交易方法**
- `buy(symbol, volume, price=None)`: 买入
- `sell(symbol, volume, price=None)`: 卖出

**数据获取**
- `get_bars(symbol, count=100)`: 获取K线数据
- `get_ticks(symbol, count=100)`: 获取Tick数据
- `get_position(symbol)`: 获取持仓信息

**参数管理**
- `get_parameter(key, default=None)`: 获取参数
- `set_parameter(key, value)`: 设置参数

**统计信息**
- `get_statistics()`: 获取策略统计

### 回测引擎 (BacktestEngine)

#### 基本用法
```python
from strategy_ai_engine.backtester import BacktestEngine
from strategies.my_strategy import MyStrategy
from datetime import date

# 创建回测引擎
engine = BacktestEngine(
    initial_capital=100000,
    commission_rate=0.0003
)

# 添加策略
strategy = MyStrategy('my_strategy', {
    'param1': 'value1',
    'param2': 10
})
engine.add_strategy(strategy)

# 加载历史数据
engine.load_data('AAPL', historical_bars)

# 运行回测
result = engine.run_backtest(
    start_date=date(2024, 1, 1),
    end_date=date(2024, 12, 31)
)

# 分析结果
print(f"总收益率: {result['total_return']:.2%}")
print(f"最大回撤: {result['max_drawdown']:.2%}")
print(f"夏普比率: {result['sharpe_ratio']:.2f}")
```

#### 回测结果字段
- `total_return`: 总收益率
- `total_pnl`: 总盈亏
- `max_drawdown`: 最大回撤
- `sharpe_ratio`: 夏普比率
- `win_rate`: 胜率
- `total_trades`: 总交易次数
- `daily_balances`: 每日余额
- `trades`: 交易记录

## 数据服务 API

### CSV连接器 (CSVConnector)

#### 基本用法
```python
from data_service.connectors.csv_connector import CSVConnector

# 创建连接器
connector = CSVConnector('csv_data', {
    'data_path': './data/csv'
})

# 连接
connector.connect()

# 订阅数据
connector.subscribe('AAPL')

# 获取历史数据
bars = connector.get_historical_bars(
    symbol='AAPL',
    start_time=datetime(2024, 1, 1),
    end_time=datetime(2024, 1, 31),
    interval='1m'
)

# 断开连接
connector.disconnect()
```

### 数据处理器 (DataProcessor)

#### K线合成
```python
from data_service.processor import DataProcessor, BarGenerator
from core.data_types import Interval

# 创建处理器
processor = DataProcessor()

# 注册K线合成器
processor.register_bar_generator('AAPL', Interval.MINUTE_1)
processor.register_bar_generator('AAPL', Interval.MINUTE_5)

# 处理Tick数据
generated_bars = processor.process_tick(tick_data)

# 获取统计信息
stats = processor.get_statistics()
```

### 数据存储 (DataStorage)

#### 基本用法
```python
from data_service.storage import DataStorage

# 创建存储管理器
storage = DataStorage('./data', 'feather')

# 保存Tick数据
storage.save_tick_data(tick_list)

# 保存K线数据
storage.save_bar_data(bar_list)

# 加载数据
ticks = storage.load_tick_data('AAPL', start_date, end_date)
bars = storage.load_bar_data('AAPL', Interval.MINUTE_1, start_date, end_date)

# 关闭存储
storage.close()
```

## 交易执行 API

### 投资组合管理器 (PortfolioManager)

#### 基本用法
```python
from execution_portfolio.portfolio_manager import PortfolioManager

# 创建管理器
portfolio = PortfolioManager(event_bus, config)
portfolio.start()

# 获取账户信息
account = portfolio.get_account_info()
print(f"余额: {account.balance}")

# 获取持仓
positions = portfolio.get_positions()
for pos in positions:
    print(f"{pos.symbol}: {pos.volume} @ {pos.price}")

# 获取统计信息
stats = portfolio.get_statistics()
```

### 订单执行器 (OrderExecutor)

#### 基本用法
```python
from execution_portfolio.order_executor import OrderExecutor

# 创建执行器
executor = OrderExecutor(event_bus, config)
executor.start()

# 获取活跃订单
orders = executor.get_active_orders()

# 取消订单
executor.cancel_order('ORDER_ID')

# 获取统计信息
stats = executor.get_statistics()
```

## 配置管理 API

### 配置管理器 (ConfigManager)

#### 基本用法
```python
from infrastructure.config_manager import ConfigManager

# 创建配置管理器
config = ConfigManager()

# 获取配置
log_level = config.get('logging.level', 'INFO')
initial_capital = config.get('execution.initial_capital', 1000000)

# 设置配置
config.set('my_setting.value', 'test')

# 保存配置
config.save_user_config()
```

## 日志系统 API

### 日志设置

#### 基本用法
```python
from infrastructure.logger import setup_logger, get_logger

# 设置日志
logger = setup_logger(
    name='my_module',
    level='INFO',
    log_file='./logs/my_module.log',
    enable_console=True,
    enable_events=True
)

# 使用日志
logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")

# 获取已存在的日志器
logger = get_logger('my_module')
```

### 日志混入类

#### 基本用法
```python
from infrastructure.logger import LoggerMixin

class MyClass(LoggerMixin):
    def __init__(self):
        super().__init__()
    
    def do_something(self):
        self.logger.info("执行某些操作")
        self.logger.debug("调试信息")
```

## 事件类型 API

### 常用事件类型

#### 数据事件
```python
from core.event_types import TickEvent, BarEvent

# Tick事件
tick_event = TickEvent(data=tick_data, source='data_source')

# K线事件
bar_event = BarEvent(data=bar_data, source='data_source')
```

#### 交易事件
```python
from core.event_types import SignalEvent, OrderEvent, TradeEvent

# 信号事件
signal = SignalEvent(
    symbol='AAPL',
    direction='LONG',
    volume=100,
    price=150.0,
    strategy_name='my_strategy'
)

# 订单事件
order_event = OrderEvent(data=order_data, source='executor')

# 成交事件
trade_event = TradeEvent(data=trade_data, source='executor')
```

#### 系统事件
```python
from core.event_types import LogEvent, SystemStatusEvent

# 日志事件
log_event = LogEvent(
    level='INFO',
    message='系统启动',
    module='main'
)

# 系统状态事件
status_event = SystemStatusEvent(
    status='running',
    message='系统正常运行',
    component='main'
)
```

## 错误处理

### 异常类型
```python
# 数据相关异常
class DataError(Exception):
    pass

# 策略相关异常
class StrategyError(Exception):
    pass

# 交易相关异常
class TradingError(Exception):
    pass
```

### 错误处理示例
```python
try:
    # 执行交易操作
    result = strategy.buy('AAPL', 100)
except TradingError as e:
    logger.error(f"交易失败: {e}")
except Exception as e:
    logger.error(f"未知错误: {e}")
```

## 性能优化

### 内存管理
```python
# 限制数据缓存大小
strategy.bars['AAPL'] = strategy.bars['AAPL'][-1000:]  # 只保留最近1000条

# 使用生成器处理大数据
def process_large_dataset(data_iterator):
    for item in data_iterator:
        yield process_item(item)
```

### 并发处理
```python
import threading
from concurrent.futures import ThreadPoolExecutor

# 使用线程池
with ThreadPoolExecutor(max_workers=4) as executor:
    futures = [executor.submit(process_data, data) for data in data_list]
    results = [future.result() for future in futures]
```

## 扩展开发

### 自定义连接器
```python
from data_service.connectors.base_connector import BaseConnector

class MyConnector(BaseConnector):
    def connect(self):
        # 实现连接逻辑
        return True
    
    def subscribe(self, symbol):
        # 实现订阅逻辑
        return True
    
    def get_historical_bars(self, symbol, start_time, end_time, interval):
        # 实现历史数据获取
        return []
```

### 自定义指标
```python
def my_indicator(prices, period=20):
    """自定义技术指标"""
    if len(prices) < period:
        return None
    
    # 计算指标值
    result = sum(prices[-period:]) / period
    return result
```

这个API文档涵盖了系统的主要接口和使用方法。开发者可以根据这些API来扩展系统功能或集成到其他应用中。
