"""
风险管理器
实现多层次风险控制机制
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
from decimal import Decimal
from datetime import datetime, date

from core.event_types import (
    OrderRequestEvent, PositionUpdateEvent, RiskWarningEvent,
    OrderRejectedEvent
)
from core.data_types import OrderData, PositionData, AccountData, Direction


class RiskManager:
    """
    风险管理器
    
    职责:
    1. 事前风控：订单风险检查
    2. 事后监控：持仓风险监控
    3. 风险预警：触发风险警报
    4. 紧急处理：自动风控措施
    """
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 风险配置
        self.risk_config = config.get('risk_management', {})
        self.enabled = self.risk_config.get('enabled', True)
        
        # 风险限制
        self.max_position_size = self.risk_config.get('max_position_size', 0.1)  # 最大仓位比例
        self.max_daily_loss = self.risk_config.get('max_daily_loss', 0.05)      # 最大日损失比例
        self.max_drawdown = self.risk_config.get('max_drawdown', 0.2)           # 最大回撤比例
        self.max_order_value = self.risk_config.get('max_order_value', 100000)  # 最大单笔订单金额
        self.max_daily_orders = self.risk_config.get('max_daily_orders', 100)   # 最大日订单数
        
        # 状态跟踪
        self.current_account: Optional[AccountData] = None
        self.current_positions: List[PositionData] = []
        self.daily_orders_count = 0
        self.daily_loss = Decimal('0')
        self.max_account_value = Decimal('0')
        self.last_reset_date = date.today()
        
        # 黑名单
        self.blacklist_symbols: List[str] = self.risk_config.get('blacklist_symbols', [])
        
        # 订阅事件
        self._subscribe_events()
        
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("order_request", self._on_order_request, "risk_manager_order")
            self.event_bus.subscribe("position_update", self._on_position_update, "risk_manager_position")
            self.event_bus.subscribe("account_update", self._on_account_update, "risk_manager_account")
    
    def _on_order_request(self, event: OrderRequestEvent) -> None:
        """处理订单请求事件"""
        if not self.enabled:
            return
            
        try:
            order_data = event.data
            
            # 执行事前风控检查
            risk_result = self.check_order_risk(order_data)
            
            if not risk_result['allowed']:
                # 拒绝订单
                self._reject_order(order_data, risk_result['reason'])
            else:
                # 通过风控，更新统计
                self._update_order_stats(order_data)
                
        except Exception as e:
            self.logger.error(f"处理订单风控失败: {e}")
            
    def _on_position_update(self, event: PositionUpdateEvent) -> None:
        """处理持仓更新事件"""
        try:
            position = event.data
            
            # 更新持仓列表
            self._update_position_list(position)
            
            # 检查持仓风险
            self._check_position_risk()
            
        except Exception as e:
            self.logger.error(f"处理持仓风控失败: {e}")
            
    def _on_account_update(self, event: AccountUpdateEvent) -> None:
        """处理账户更新事件"""
        try:
            self.current_account = event.data
            
            # 更新最大账户价值
            if self.current_account.balance > self.max_account_value:
                self.max_account_value = self.current_account.balance
                
            # 检查账户风险
            self._check_account_risk()
            
        except Exception as e:
            self.logger.error(f"处理账户风控失败: {e}")
    
    def check_order_risk(self, order: OrderData) -> Dict[str, Any]:
        """
        检查订单风险
        
        Args:
            order: 订单数据
            
        Returns:
            Dict: {'allowed': bool, 'reason': str}
        """
        try:
            # 重置日统计（如果需要）
            self._reset_daily_stats_if_needed()
            
            # 1. 检查品种黑名单
            if order.symbol in self.blacklist_symbols:
                return {'allowed': False, 'reason': f'品种{order.symbol}在黑名单中'}
            
            # 2. 检查单笔订单金额
            order_value = float(order.price * order.volume)
            if order_value > self.max_order_value:
                return {'allowed': False, 'reason': f'订单金额{order_value:.2f}超过限制{self.max_order_value}'}
            
            # 3. 检查日订单数量
            if self.daily_orders_count >= self.max_daily_orders:
                return {'allowed': False, 'reason': f'日订单数{self.daily_orders_count}已达上限{self.max_daily_orders}'}
            
            # 4. 检查仓位限制
            if self.current_account:
                position_risk = self._check_position_size_risk(order)
                if not position_risk['allowed']:
                    return position_risk
            
            # 5. 检查资金充足性
            if self.current_account:
                fund_risk = self._check_fund_risk(order)
                if not fund_risk['allowed']:
                    return fund_risk
            
            return {'allowed': True, 'reason': '通过风控检查'}
            
        except Exception as e:
            self.logger.error(f"订单风控检查失败: {e}")
            return {'allowed': False, 'reason': f'风控检查异常: {e}'}
    
    def _check_position_size_risk(self, order: OrderData) -> Dict[str, Any]:
        """检查仓位大小风险"""
        try:
            if not self.current_account:
                return {'allowed': True, 'reason': '无账户信息'}
            
            # 计算订单后的仓位价值
            order_value = float(order.price * order.volume)
            
            # 获取当前该品种的仓位
            current_position_value = 0
            for pos in self.current_positions:
                if pos.symbol == order.symbol:
                    current_position_value += float(pos.price * pos.volume)
            
            # 计算新的仓位价值
            if order.direction == Direction.LONG:
                new_position_value = current_position_value + order_value
            else:
                new_position_value = max(0, current_position_value - order_value)
            
            # 检查仓位比例
            position_ratio = new_position_value / float(self.current_account.balance)
            if position_ratio > self.max_position_size:
                return {
                    'allowed': False, 
                    'reason': f'仓位比例{position_ratio:.2%}超过限制{self.max_position_size:.2%}'
                }
            
            return {'allowed': True, 'reason': '仓位检查通过'}
            
        except Exception as e:
            self.logger.error(f"仓位风险检查失败: {e}")
            return {'allowed': False, 'reason': f'仓位检查异常: {e}'}
    
    def _check_fund_risk(self, order: OrderData) -> Dict[str, Any]:
        """检查资金风险"""
        try:
            if not self.current_account:
                return {'allowed': True, 'reason': '无账户信息'}
            
            # 计算所需资金
            required_fund = float(order.price * order.volume)
            
            # 检查可用资金
            if required_fund > float(self.current_account.available):
                return {
                    'allowed': False,
                    'reason': f'资金不足，需要{required_fund:.2f}，可用{self.current_account.available:.2f}'
                }
            
            return {'allowed': True, 'reason': '资金检查通过'}
            
        except Exception as e:
            self.logger.error(f"资金风险检查失败: {e}")
            return {'allowed': False, 'reason': f'资金检查异常: {e}'}
    
    def _check_position_risk(self) -> None:
        """检查持仓风险"""
        try:
            if not self.current_account or not self.current_positions:
                return
            
            # 计算总持仓价值
            total_position_value = sum(
                float(pos.price * pos.volume) for pos in self.current_positions
            )
            
            # 计算持仓比例
            position_ratio = total_position_value / float(self.current_account.balance)
            
            if position_ratio > self.max_position_size * 0.9:  # 90%预警
                self._send_risk_warning(
                    f"持仓比例{position_ratio:.2%}接近限制{self.max_position_size:.2%}",
                    "POSITION_HIGH"
                )
                
        except Exception as e:
            self.logger.error(f"持仓风险检查失败: {e}")
    
    def _check_account_risk(self) -> None:
        """检查账户风险"""
        try:
            if not self.current_account:
                return
            
            # 检查回撤
            if self.max_account_value > 0:
                current_drawdown = (self.max_account_value - self.current_account.balance) / self.max_account_value
                
                if current_drawdown > self.max_drawdown:
                    self._send_risk_warning(
                        f"账户回撤{current_drawdown:.2%}超过限制{self.max_drawdown:.2%}",
                        "DRAWDOWN_EXCEEDED"
                    )
                elif current_drawdown > self.max_drawdown * 0.8:  # 80%预警
                    self._send_risk_warning(
                        f"账户回撤{current_drawdown:.2%}接近限制{self.max_drawdown:.2%}",
                        "DRAWDOWN_HIGH"
                    )
            
            # 检查日损失
            daily_loss_ratio = float(self.daily_loss / self.current_account.balance) if self.current_account.balance > 0 else 0
            if daily_loss_ratio > self.max_daily_loss:
                self._send_risk_warning(
                    f"日损失{daily_loss_ratio:.2%}超过限制{self.max_daily_loss:.2%}",
                    "DAILY_LOSS_EXCEEDED"
                )
                
        except Exception as e:
            self.logger.error(f"账户风险检查失败: {e}")
    
    def _update_position_list(self, position: PositionData) -> None:
        """更新持仓列表"""
        # 查找并更新现有持仓
        for i, pos in enumerate(self.current_positions):
            if (pos.symbol == position.symbol and 
                pos.direction == position.direction):
                
                if position.volume == 0:
                    # 删除空持仓
                    self.current_positions.pop(i)
                else:
                    # 更新持仓
                    self.current_positions[i] = position
                return
        
        # 新持仓
        if position.volume > 0:
            self.current_positions.append(position)
    
    def _update_order_stats(self, order: OrderData) -> None:
        """更新订单统计"""
        self.daily_orders_count += 1
        
    def _reset_daily_stats_if_needed(self) -> None:
        """重置日统计（如果需要）"""
        today = date.today()
        if today != self.last_reset_date:
            self.daily_orders_count = 0
            self.daily_loss = Decimal('0')
            self.last_reset_date = today
            self.logger.info("重置日风控统计")
    
    def _reject_order(self, order: OrderData, reason: str) -> None:
        """拒绝订单"""
        try:
            # 发布订单拒绝事件
            reject_event = OrderRejectedEvent(order, reason)
            if self.event_bus:
                self.event_bus.publish(reject_event)
            
            self.logger.warning(f"拒绝订单 {order.order_id}: {reason}")
            
        except Exception as e:
            self.logger.error(f"拒绝订单失败: {e}")
    
    def _send_risk_warning(self, message: str, warning_type: str) -> None:
        """发送风险警告"""
        try:
            warning_event = RiskWarningEvent(message, warning_type, datetime.now())
            if self.event_bus:
                self.event_bus.publish(warning_event)
            
            self.logger.warning(f"风险警告 [{warning_type}]: {message}")
            
        except Exception as e:
            self.logger.error(f"发送风险警告失败: {e}")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """获取风险状态"""
        try:
            status = {
                'enabled': self.enabled,
                'daily_orders': self.daily_orders_count,
                'max_daily_orders': self.max_daily_orders,
                'daily_loss': float(self.daily_loss),
                'max_daily_loss_ratio': self.max_daily_loss,
                'position_count': len(self.current_positions),
                'max_position_size': self.max_position_size
            }
            
            if self.current_account:
                status['account_balance'] = float(self.current_account.balance)
                status['available_fund'] = float(self.current_account.available)
                
                if self.max_account_value > 0:
                    current_drawdown = (self.max_account_value - self.current_account.balance) / self.max_account_value
                    status['current_drawdown'] = float(current_drawdown)
                    status['max_drawdown'] = self.max_drawdown
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取风险状态失败: {e}")
            return {'error': str(e)}
    
    def set_risk_limit(self, limit_type: str, value: float) -> bool:
        """设置风险限制"""
        try:
            if limit_type == 'max_position_size':
                self.max_position_size = value
            elif limit_type == 'max_daily_loss':
                self.max_daily_loss = value
            elif limit_type == 'max_drawdown':
                self.max_drawdown = value
            elif limit_type == 'max_order_value':
                self.max_order_value = value
            elif limit_type == 'max_daily_orders':
                self.max_daily_orders = int(value)
            else:
                self.logger.warning(f"未知的风险限制类型: {limit_type}")
                return False
            
            self.logger.info(f"设置风险限制 {limit_type} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置风险限制失败: {e}")
            return False
