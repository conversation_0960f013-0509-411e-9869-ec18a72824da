"""
基础技术指标实现
"""

from __future__ import annotations
from typing import List, Optional, Tuple
import numpy as np
import pandas as pd
from decimal import Decimal


class BaseIndicator:
    """指标基类"""
    
    def __init__(self, period: int):
        self.period = period
        self.values: List[float] = []
        self.is_ready = False
    
    def update(self, value: float) -> Optional[float]:
        """更新指标值"""
        raise NotImplementedError
    
    def get_value(self) -> Optional[float]:
        """获取当前指标值"""
        return self.values[-1] if self.values else None
    
    def get_values(self, count: int = None) -> List[float]:
        """获取历史指标值"""
        if count is None:
            return self.values.copy()
        return self.values[-count:] if len(self.values) >= count else self.values.copy()
    
    def reset(self) -> None:
        """重置指标"""
        self.values.clear()
        self.is_ready = False


class SMA(BaseIndicator):
    """简单移动平均线"""
    
    def __init__(self, period: int):
        super().__init__(period)
        self.data: List[float] = []
    
    def update(self, value: float) -> Optional[float]:
        """更新SMA值"""
        self.data.append(value)
        
        if len(self.data) > self.period:
            self.data.pop(0)
        
        if len(self.data) == self.period:
            sma_value = sum(self.data) / self.period
            self.values.append(sma_value)
            self.is_ready = True
            return sma_value
        
        return None


class EMA(BaseIndicator):
    """指数移动平均线"""
    
    def __init__(self, period: int):
        super().__init__(period)
        self.multiplier = 2.0 / (period + 1)
        self.ema_value: Optional[float] = None
    
    def update(self, value: float) -> Optional[float]:
        """更新EMA值"""
        if self.ema_value is None:
            self.ema_value = value
        else:
            self.ema_value = (value * self.multiplier) + (self.ema_value * (1 - self.multiplier))
        
        self.values.append(self.ema_value)
        self.is_ready = True
        return self.ema_value


class RSI(BaseIndicator):
    """相对强弱指数"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.gains: List[float] = []
        self.losses: List[float] = []
        self.prev_close: Optional[float] = None
    
    def update(self, close: float) -> Optional[float]:
        """更新RSI值"""
        if self.prev_close is not None:
            change = close - self.prev_close
            gain = max(change, 0)
            loss = max(-change, 0)
            
            self.gains.append(gain)
            self.losses.append(loss)
            
            if len(self.gains) > self.period:
                self.gains.pop(0)
                self.losses.pop(0)
            
            if len(self.gains) == self.period:
                avg_gain = sum(self.gains) / self.period
                avg_loss = sum(self.losses) / self.period
                
                if avg_loss == 0:
                    rsi_value = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi_value = 100 - (100 / (1 + rs))
                
                self.values.append(rsi_value)
                self.is_ready = True
                self.prev_close = close
                return rsi_value
        
        self.prev_close = close
        return None


class MACD(BaseIndicator):
    """MACD指标"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        super().__init__(slow_period)
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
        self.fast_ema = EMA(fast_period)
        self.slow_ema = EMA(slow_period)
        self.signal_ema = EMA(signal_period)
        
        self.macd_line: List[float] = []
        self.signal_line: List[float] = []
        self.histogram: List[float] = []
    
    def update(self, close: float) -> Optional[Tuple[float, float, float]]:
        """更新MACD值，返回(MACD线, 信号线, 柱状图)"""
        fast_value = self.fast_ema.update(close)
        slow_value = self.slow_ema.update(close)
        
        if fast_value is not None and slow_value is not None:
            macd_value = fast_value - slow_value
            self.macd_line.append(macd_value)
            
            signal_value = self.signal_ema.update(macd_value)
            if signal_value is not None:
                self.signal_line.append(signal_value)
                histogram_value = macd_value - signal_value
                self.histogram.append(histogram_value)
                
                result = (macd_value, signal_value, histogram_value)
                self.values.append(result)
                self.is_ready = True
                return result
        
        return None
    
    def get_macd_line(self) -> List[float]:
        """获取MACD线"""
        return self.macd_line.copy()
    
    def get_signal_line(self) -> List[float]:
        """获取信号线"""
        return self.signal_line.copy()
    
    def get_histogram(self) -> List[float]:
        """获取柱状图"""
        return self.histogram.copy()


class BollingerBands(BaseIndicator):
    """布林带"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        super().__init__(period)
        self.std_dev = std_dev
        self.data: List[float] = []
        self.upper_band: List[float] = []
        self.middle_band: List[float] = []
        self.lower_band: List[float] = []
    
    def update(self, close: float) -> Optional[Tuple[float, float, float]]:
        """更新布林带值，返回(上轨, 中轨, 下轨)"""
        self.data.append(close)
        
        if len(self.data) > self.period:
            self.data.pop(0)
        
        if len(self.data) == self.period:
            mean = sum(self.data) / self.period
            variance = sum((x - mean) ** 2 for x in self.data) / self.period
            std = variance ** 0.5
            
            upper = mean + (self.std_dev * std)
            middle = mean
            lower = mean - (self.std_dev * std)
            
            self.upper_band.append(upper)
            self.middle_band.append(middle)
            self.lower_band.append(lower)
            
            result = (upper, middle, lower)
            self.values.append(result)
            self.is_ready = True
            return result
        
        return None
    
    def get_upper_band(self) -> List[float]:
        """获取上轨"""
        return self.upper_band.copy()
    
    def get_middle_band(self) -> List[float]:
        """获取中轨"""
        return self.middle_band.copy()
    
    def get_lower_band(self) -> List[float]:
        """获取下轨"""
        return self.lower_band.copy()


class ATR(BaseIndicator):
    """平均真实波幅"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.true_ranges: List[float] = []
        self.prev_close: Optional[float] = None
    
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新ATR值"""
        if self.prev_close is not None:
            tr1 = high - low
            tr2 = abs(high - self.prev_close)
            tr3 = abs(low - self.prev_close)
            true_range = max(tr1, tr2, tr3)
            
            self.true_ranges.append(true_range)
            
            if len(self.true_ranges) > self.period:
                self.true_ranges.pop(0)
            
            if len(self.true_ranges) == self.period:
                atr_value = sum(self.true_ranges) / self.period
                self.values.append(atr_value)
                self.is_ready = True
                self.prev_close = close
                return atr_value
        
        self.prev_close = close
        return None


class Stochastic(BaseIndicator):
    """随机指标"""
    
    def __init__(self, k_period: int = 14, d_period: int = 3):
        super().__init__(k_period)
        self.k_period = k_period
        self.d_period = d_period
        self.highs: List[float] = []
        self.lows: List[float] = []
        self.closes: List[float] = []
        self.k_values: List[float] = []
        self.d_sma = SMA(d_period)
    
    def update(self, high: float, low: float, close: float) -> Optional[Tuple[float, float]]:
        """更新随机指标值，返回(%K, %D)"""
        self.highs.append(high)
        self.lows.append(low)
        self.closes.append(close)
        
        if len(self.highs) > self.k_period:
            self.highs.pop(0)
            self.lows.pop(0)
            self.closes.pop(0)
        
        if len(self.highs) == self.k_period:
            highest_high = max(self.highs)
            lowest_low = min(self.lows)
            
            if highest_high == lowest_low:
                k_value = 50
            else:
                k_value = ((close - lowest_low) / (highest_high - lowest_low)) * 100
            
            self.k_values.append(k_value)
            
            d_value = self.d_sma.update(k_value)
            if d_value is not None:
                result = (k_value, d_value)
                self.values.append(result)
                self.is_ready = True
                return result
        
        return None


class WilliamsR(BaseIndicator):
    """威廉指标"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.highs: List[float] = []
        self.lows: List[float] = []
    
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新威廉指标值"""
        self.highs.append(high)
        self.lows.append(low)
        
        if len(self.highs) > self.period:
            self.highs.pop(0)
            self.lows.pop(0)
        
        if len(self.highs) == self.period:
            highest_high = max(self.highs)
            lowest_low = min(self.lows)
            
            if highest_high == lowest_low:
                wr_value = -50
            else:
                wr_value = ((highest_high - close) / (highest_high - lowest_low)) * -100
            
            self.values.append(wr_value)
            self.is_ready = True
            return wr_value
        
        return None


class CCI(BaseIndicator):
    """商品通道指数"""
    
    def __init__(self, period: int = 20):
        super().__init__(period)
        self.typical_prices: List[float] = []
    
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新CCI值"""
        typical_price = (high + low + close) / 3
        self.typical_prices.append(typical_price)
        
        if len(self.typical_prices) > self.period:
            self.typical_prices.pop(0)
        
        if len(self.typical_prices) == self.period:
            sma = sum(self.typical_prices) / self.period
            mean_deviation = sum(abs(tp - sma) for tp in self.typical_prices) / self.period
            
            if mean_deviation == 0:
                cci_value = 0
            else:
                cci_value = (typical_price - sma) / (0.015 * mean_deviation)
            
            self.values.append(cci_value)
            self.is_ready = True
            return cci_value
        
        return None


class ROC(BaseIndicator):
    """变化率指标"""
    
    def __init__(self, period: int = 12):
        super().__init__(period)
        self.prices: List[float] = []
    
    def update(self, close: float) -> Optional[float]:
        """更新ROC值"""
        self.prices.append(close)
        
        if len(self.prices) > self.period + 1:
            self.prices.pop(0)
        
        if len(self.prices) == self.period + 1:
            old_price = self.prices[0]
            current_price = self.prices[-1]
            
            if old_price == 0:
                roc_value = 0
            else:
                roc_value = ((current_price - old_price) / old_price) * 100
            
            self.values.append(roc_value)
            self.is_ready = True
            return roc_value
        
        return None
